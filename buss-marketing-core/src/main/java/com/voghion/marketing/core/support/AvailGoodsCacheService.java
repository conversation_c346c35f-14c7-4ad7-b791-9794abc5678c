package com.voghion.marketing.core.support;

import com.alibaba.fastjson.JSON;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.model.PageView;
import com.google.common.collect.Lists;
import com.voghion.marketing.model.vo.AvailGoodsForShopVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 商家可报名商品缓存服务
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-17
 */
@Service
@Slf4j
public class AvailGoodsCacheService {

    @Resource
    private RedisApi redisApi;

    private static final String CACHE_PREFIX = "avail_goods:";
    private static final String DAILY_CACHE_PREFIX = "avail_goods_daily:";
    private static final String LOCK_PREFIX = "avail_goods_lock:";
    
    // 缓存时间配置
    private static final int NORMAL_CACHE_SECONDS = 30 * 60; // 30分钟
    private static final int DAILY_CACHE_SECONDS = 24 * 60 * 60; // 24小时
    private static final int LOCK_SECONDS = 5 * 60; // 5分钟锁定时间

    /**
     * 获取商家可报名商品缓存
     * 
     * @param activityId 活动ID
     * @param shopId 商家ID
     * @return 缓存的商品列表，如果不存在返回null
     */
    public PageView<AvailGoodsForShopVO> getAvailGoodsCache(Long activityId, Long shopId) {
        String cacheKey = buildCacheKey(activityId, shopId);
        try {
            String cachedData = redisApi.get(cacheKey);
            if (StringUtils.isNotBlank(cachedData)) {
                log.info("getAvailGoodsCache hit cache, activityId: {}, shopId: {}", activityId, shopId);
                return JSON.parseObject(cachedData, PageView.class);
            }
        } catch (Exception e) {
            log.error("getAvailGoodsCache error, activityId: {}, shopId: {}", activityId, shopId, e);
        }
        return null;
    }

    /**
     * 设置商家可报名商品缓存
     * 
     * @param activityId 活动ID
     * @param shopId 商家ID
     * @param data 商品列表数据
     */
    public void setAvailGoodsCache(Long activityId, Long shopId, PageView<AvailGoodsForShopVO> data) {
        String cacheKey = buildCacheKey(activityId, shopId);
        try {
            redisApi.setex(cacheKey, JSON.toJSONString(data), NORMAL_CACHE_SECONDS);
            log.info("setAvailGoodsCache success, activityId: {}, shopId: {}, size: {}", 
                    activityId, shopId, data.getTotal());
        } catch (Exception e) {
            log.error("setAvailGoodsCache error, activityId: {}, shopId: {}", activityId, shopId, e);
        }
    }

    /**
     * 获取每日缓存的商家可报名商品
     * 
     * @param activityId 活动ID
     * @param shopId 商家ID
     * @return 缓存的商品列表，如果不存在返回null
     */
    public PageView<AvailGoodsForShopVO> getDailyAvailGoodsCache(Long activityId, Long shopId) {
        String cacheKey = buildDailyCacheKey(activityId, shopId);
        try {
            String cachedData = redisApi.get(cacheKey);
            if (StringUtils.isNotBlank(cachedData)) {
                log.info("getDailyAvailGoodsCache hit cache, activityId: {}, shopId: {}", activityId, shopId);
                return JSON.parseObject(cachedData, PageView.class);
            }
        } catch (Exception e) {
            log.error("getDailyAvailGoodsCache error, activityId: {}, shopId: {}", activityId, shopId, e);
        }
        return null;
    }

    /**
     * 设置每日缓存的商家可报名商品
     * 
     * @param activityId 活动ID
     * @param shopId 商家ID
     * @param data 商品列表数据
     */
    public void setDailyAvailGoodsCache(Long activityId, Long shopId, PageView<AvailGoodsForShopVO> data) {
        String cacheKey = buildDailyCacheKey(activityId, shopId);
        try {
            redisApi.setex(cacheKey, JSON.toJSONString(data), DAILY_CACHE_SECONDS);
            log.info("setDailyAvailGoodsCache success, activityId: {}, shopId: {}, size: {}", 
                    activityId, shopId, data.getTotal());
        } catch (Exception e) {
            log.error("setDailyAvailGoodsCache error, activityId: {}, shopId: {}", activityId, shopId, e);
        }
    }

    /**
     * 尝试获取分布式锁
     * 
     * @param activityId 活动ID
     * @param shopId 商家ID
     * @return 是否获取成功
     */
    public boolean tryLock(Long activityId, Long shopId) {
        String lockKey = buildLockKey(activityId, shopId);
        try {
            String lockValue = String.valueOf(System.currentTimeMillis());
            Boolean success = redisApi.setnx(lockKey, lockValue, LOCK_SECONDS);
            if (Boolean.TRUE.equals(success)) {
                log.info("tryLock success, activityId: {}, shopId: {}", activityId, shopId);
                return true;
            }
        } catch (Exception e) {
            log.error("tryLock error, activityId: {}, shopId: {}", activityId, shopId, e);
        }
        return false;
    }

    /**
     * 释放分布式锁
     * 
     * @param activityId 活动ID
     * @param shopId 商家ID
     */
    public void releaseLock(Long activityId, Long shopId) {
        String lockKey = buildLockKey(activityId, shopId);
        try {
            redisApi.del(lockKey);
            log.info("releaseLock success, activityId: {}, shopId: {}", activityId, shopId);
        } catch (Exception e) {
            log.error("releaseLock error, activityId: {}, shopId: {}", activityId, shopId, e);
        }
    }

    /**
     * 清除商家可报名商品缓存
     * 
     * @param activityId 活动ID
     * @param shopId 商家ID
     */
    public void clearAvailGoodsCache(Long activityId, Long shopId) {
        try {
            String normalCacheKey = buildCacheKey(activityId, shopId);
            String dailyCacheKey = buildDailyCacheKey(activityId, shopId);
            
            redisApi.del(normalCacheKey);
            redisApi.del(dailyCacheKey);
            
            log.info("clearAvailGoodsCache success, activityId: {}, shopId: {}", activityId, shopId);
        } catch (Exception e) {
            log.error("clearAvailGoodsCache error, activityId: {}, shopId: {}", activityId, shopId, e);
        }
    }

    /**
     * 清除活动相关的所有缓存
     * 
     * @param activityId 活动ID
     */
    public void clearActivityCache(Long activityId) {
        try {
            String pattern1 = CACHE_PREFIX + activityId + ":*";
            String pattern2 = DAILY_CACHE_PREFIX + activityId + ":*";
            
            // 注意：这里需要根据实际的Redis客户端实现来删除匹配的key
            // 示例代码，实际实现可能需要调整
            log.info("clearActivityCache activityId: {}", activityId);
        } catch (Exception e) {
            log.error("clearActivityCache error, activityId: {}", activityId, e);
        }
    }

    /**
     * 构建缓存key
     */
    private String buildCacheKey(Long activityId, Long shopId) {
        return CACHE_PREFIX + activityId + ":" + shopId;
    }

    /**
     * 构建每日缓存key
     */
    private String buildDailyCacheKey(Long activityId, Long shopId) {
        String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return DAILY_CACHE_PREFIX + activityId + ":" + shopId + ":" + today;
    }

    /**
     * 构建锁key
     */
    private String buildLockKey(Long activityId, Long shopId) {
        return LOCK_PREFIX + activityId + ":" + shopId;
    }

    /**
     * 检查缓存是否存在
     * 
     * @param activityId 活动ID
     * @param shopId 商家ID
     * @return 是否存在缓存
     */
    public boolean hasCacheData(Long activityId, Long shopId) {
        String cacheKey = buildCacheKey(activityId, shopId);
        String dailyCacheKey = buildDailyCacheKey(activityId, shopId);
        
        try {
            return redisApi.exists(cacheKey) || redisApi.exists(dailyCacheKey);
        } catch (Exception e) {
            log.error("hasCacheData error, activityId: {}, shopId: {}", activityId, shopId, e);
            return false;
        }
    }

    /**
     * 获取缓存统计信息
     * 
     * @param activityId 活动ID
     * @param shopId 商家ID
     * @return 缓存统计信息
     */
    public String getCacheStats(Long activityId, Long shopId) {
        try {
            String normalKey = buildCacheKey(activityId, shopId);
            String dailyKey = buildDailyCacheKey(activityId, shopId);
            
            boolean hasNormal = redisApi.exists(normalKey);
            boolean hasDaily = redisApi.exists(dailyKey);
            
            return String.format("Normal cache: %s, Daily cache: %s", hasNormal, hasDaily);
        } catch (Exception e) {
            log.error("getCacheStats error, activityId: {}, shopId: {}", activityId, shopId, e);
            return "Cache stats unavailable";
        }
    }
}
