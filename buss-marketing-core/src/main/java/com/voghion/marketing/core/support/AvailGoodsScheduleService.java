package com.voghion.marketing.core.support;

import com.alibaba.fastjson.JSON;
import com.colorlight.base.model.PageView;
import com.google.common.collect.Lists;
import com.voghion.marketing.core.NewActivityGoodsCoreService;
import com.voghion.marketing.model.po.NewActivity;
import com.voghion.marketing.model.vo.AvailGoodsForShopQueryVO;
import com.voghion.marketing.model.vo.AvailGoodsForShopVO;
import com.voghion.marketing.service.NewActivityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * 商家可报名商品定时任务服务
 * 负责每日更新商家可报名商品缓存
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-17
 */
@Service
@Slf4j
public class AvailGoodsScheduleService {

    @Resource
    private NewActivityService newActivityService;

    @Resource
    private NewActivityGoodsCoreService newActivityGoodsCoreService;

    @Resource
    private AvailGoodsCacheService availGoodsCacheService;

    /**
     * 每日凌晨2点更新商家可报名商品缓存
     * 为所有进行中的活动预生成商家可报名商品列表
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dailyUpdateAvailGoodsCache() {
        log.info("dailyUpdateAvailGoodsCache start at {}", LocalDateTime.now());
        
        try {
            // 1. 获取所有进行中的活动
            List<NewActivity> ongoingActivities = getOngoingActivities();
            if (CollectionUtils.isEmpty(ongoingActivities)) {
                log.info("dailyUpdateAvailGoodsCache no ongoing activities found");
                return;
            }

            log.info("dailyUpdateAvailGoodsCache found {} ongoing activities", ongoingActivities.size());

            // 2. 为每个活动更新缓存
            for (NewActivity activity : ongoingActivities) {
                try {
                    updateActivityAvailGoodsCache(activity);
                } catch (Exception e) {
                    log.error("dailyUpdateAvailGoodsCache error for activity {}", activity.getId(), e);
                }
            }

            log.info("dailyUpdateAvailGoodsCache completed at {}", LocalDateTime.now());

        } catch (Exception e) {
            log.error("dailyUpdateAvailGoodsCache error", e);
        }
    }

    /**
     * 每小时清理过期缓存
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void hourlyCleanExpiredCache() {
        log.info("hourlyCleanExpiredCache start at {}", LocalDateTime.now());
        
        try {
            // 获取已结束的活动
            List<NewActivity> endedActivities = getEndedActivities();
            
            for (NewActivity activity : endedActivities) {
                try {
                    availGoodsCacheService.clearActivityCache(activity.getId());
                    log.info("hourlyCleanExpiredCache cleared cache for ended activity {}", activity.getId());
                } catch (Exception e) {
                    log.error("hourlyCleanExpiredCache error for activity {}", activity.getId(), e);
                }
            }

            log.info("hourlyCleanExpiredCache completed, cleaned {} activities", endedActivities.size());

        } catch (Exception e) {
            log.error("hourlyCleanExpiredCache error", e);
        }
    }

    /**
     * 为指定活动更新商家可报名商品缓存
     * 
     * @param activity 活动信息
     */
    @Async
    public void updateActivityAvailGoodsCache(NewActivity activity) {
        log.info("updateActivityAvailGoodsCache start for activity {}", activity.getId());
        
        try {
            // 1. 获取该活动的所有参与商家
            Set<Long> shopIds = getActivityShopIds(activity.getId());
            if (CollectionUtils.isEmpty(shopIds)) {
                log.info("updateActivityAvailGoodsCache no shops found for activity {}", activity.getId());
                return;
            }

            log.info("updateActivityAvailGoodsCache found {} shops for activity {}", shopIds.size(), activity.getId());

            // 2. 为每个商家生成可报名商品缓存
            List<CompletableFuture<Void>> futures = Lists.newArrayList();
            
            for (Long shopId : shopIds) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        updateShopAvailGoodsCache(activity.getId(), shopId);
                    } catch (Exception e) {
                        log.error("updateActivityAvailGoodsCache error for activity {} shop {}", 
                                activity.getId(), shopId, e);
                    }
                });
                futures.add(future);
            }

            // 3. 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            log.info("updateActivityAvailGoodsCache completed for activity {}", activity.getId());

        } catch (Exception e) {
            log.error("updateActivityAvailGoodsCache error for activity {}", activity.getId(), e);
        }
    }

    /**
     * 为指定商家和活动更新可报名商品缓存
     * 
     * @param activityId 活动ID
     * @param shopId 商家ID
     */
    private void updateShopAvailGoodsCache(Long activityId, Long shopId) {
        try {
            // 1. 尝试获取锁，避免重复处理
            if (!availGoodsCacheService.tryLock(activityId, shopId)) {
                log.info("updateShopAvailGoodsCache skip due to lock, activityId: {}, shopId: {}", 
                        activityId, shopId);
                return;
            }

            try {
                // 2. 构建查询参数
                AvailGoodsForShopQueryVO queryVO = new AvailGoodsForShopQueryVO();
                queryVO.setActivityId(activityId);
                queryVO.setShopId(shopId);
                queryVO.setPageNow(1);
                queryVO.setPageSize(10000); // 获取全量数据

                // 3. 查询商家可报名商品
                PageView<AvailGoodsForShopVO> result = newActivityGoodsCoreService.queryAvailGoodsForShop(queryVO);

                // 4. 检查商品数量限制
                if (result.getTotal() > 10000) {
                    log.warn("updateShopAvailGoodsCache goods count exceed limit, activityId: {}, shopId: {}, count: {}", 
                            activityId, shopId, result.getTotal());
                    
                    // 创建警告信息
                    AvailGoodsForShopVO warningVO = new AvailGoodsForShopVO();
                    warningVO.setCannotSignUpReason("商品数量超过1万件，请联系客服处理");
                    result = new PageView<>(Lists.newArrayList(warningVO), 1L, 1, 1);
                }

                // 5. 缓存结果
                availGoodsCacheService.setDailyAvailGoodsCache(activityId, shopId, result);

                log.info("updateShopAvailGoodsCache success, activityId: {}, shopId: {}, count: {}", 
                        activityId, shopId, result.getTotal());

            } finally {
                // 6. 释放锁
                availGoodsCacheService.releaseLock(activityId, shopId);
            }

        } catch (Exception e) {
            log.error("updateShopAvailGoodsCache error, activityId: {}, shopId: {}", activityId, shopId, e);
        }
    }

    /**
     * 获取进行中的活动列表
     */
    private List<NewActivity> getOngoingActivities() {
        return newActivityService.lambdaQuery()
                .eq(NewActivity::getActivityStatus, 20) // 进行中状态
                .eq(NewActivity::getIsDel, 0)
                .le(NewActivity::getSignStartTime, LocalDateTime.now())
                .ge(NewActivity::getSignEndTime, LocalDateTime.now())
                .list();
    }

    /**
     * 获取已结束的活动列表
     */
    private List<NewActivity> getEndedActivities() {
        return newActivityService.lambdaQuery()
                .in(NewActivity::getActivityStatus, Lists.newArrayList(40, 50)) // 已过期、已删除
                .or()
                .lt(NewActivity::getEffectEndTime, LocalDateTime.now().minusDays(1)) // 结束超过1天
                .list();
    }

    /**
     * 获取活动的参与商家ID列表
     */
    private Set<Long> getActivityShopIds(Long activityId) {
        // 这里可以根据实际业务逻辑获取参与该活动的商家ID
        // 例如：从报名记录、商家白名单、或者其他相关表中获取
        
        // 示例实现：从活动商品表中获取已报名的商家
        return newActivityGoodsCoreService.queryActivityGoodsData(
                new com.voghion.marketing.model.dto.NewActivityGoodsDataDTO() {{
                    setActivityId(activityId);
                }}
        ).stream()
        .map(goods -> goods.getShopId())
        .collect(Collectors.toSet());
    }

    /**
     * 手动触发指定活动的缓存更新
     * 
     * @param activityId 活动ID
     */
    public void manualUpdateActivityCache(Long activityId) {
        log.info("manualUpdateActivityCache start for activity {}", activityId);
        
        try {
            NewActivity activity = newActivityService.getById(activityId);
            if (activity == null) {
                log.warn("manualUpdateActivityCache activity not found: {}", activityId);
                return;
            }

            updateActivityAvailGoodsCache(activity);
            
        } catch (Exception e) {
            log.error("manualUpdateActivityCache error for activity {}", activityId, e);
        }
    }

    /**
     * 手动清理指定活动的缓存
     * 
     * @param activityId 活动ID
     */
    public void manualClearActivityCache(Long activityId) {
        log.info("manualClearActivityCache start for activity {}", activityId);
        
        try {
            availGoodsCacheService.clearActivityCache(activityId);
            log.info("manualClearActivityCache completed for activity {}", activityId);
            
        } catch (Exception e) {
            log.error("manualClearActivityCache error for activity {}", activityId, e);
        }
    }
}
