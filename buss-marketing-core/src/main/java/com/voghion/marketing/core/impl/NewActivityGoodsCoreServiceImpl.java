package com.voghion.marketing.core.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.lang.exception.CustomException;
import com.colorlight.base.model.PageView;
import com.colorlight.base.model.ThreadContext;
import com.colorlight.base.model.constants.SystemConstants;
import com.colorlight.base.model.dto.ClientInfoDTO;
import com.colorlight.base.utils.CheckUtils;
import com.colorlight.base.utils.DateUtil;
import com.colorlight.base.utils.TransferUtils;
import com.google.api.client.util.Sets;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import com.voghion.es.impl.BaseEsQueryServiceImpl;
import com.voghion.es.model.GoodsExtConfigModel;
import com.voghion.es.model.GoodsExtDetailModel;
import com.voghion.es.model.ListGoodsCommentModel;
import com.voghion.es.service.GoodsEsService;
import com.voghion.es.vo.GoodsESVo;
import com.voghion.marketing.api.dto.*;
import com.voghion.marketing.api.enums.GoodsActivityTypeEnum;
import com.voghion.marketing.biz.activity.ActivityGoods;
import com.voghion.marketing.biz.activity.ActivityGoodsSelectCheckExecutor;
import com.voghion.marketing.biz.activity.ActivityGoodsSelectCheckParam;
import com.voghion.marketing.client.*;
import com.voghion.marketing.client.service.DingDingCoreService;
import com.voghion.marketing.client.service.impl.DingDingCoreServiceImpl;
import com.voghion.marketing.core.ActivityOriginalPriceCoreService;
import com.voghion.marketing.core.NewActivityGoodsCoreService;
import com.voghion.marketing.core.SpecialActivityGoodsCoreService;
import com.voghion.marketing.core.support.AvailGoodsCacheService;
import com.voghion.marketing.core.support.ClientInfoSupportService;
import com.voghion.marketing.dto.ActivityFirstSelectDTO;
import com.voghion.marketing.enums.ActivityFirstSelectFail;
import com.voghion.marketing.enums.DigitalOperationsEnum;
import com.voghion.marketing.excel.model.ActivityGoodsSignUpFailVO;
import com.voghion.marketing.excel.model.ActivityGoodsSignUpVO;
import com.voghion.marketing.listener.ActivityGoodsSignUpByImportVo;
import com.voghion.marketing.listener.ActivityGoodsSortVo;
import com.voghion.marketing.model.dto.*;
import com.voghion.marketing.model.enums.*;
import com.voghion.marketing.model.po.*;
import com.voghion.marketing.model.vo.*;
import com.voghion.marketing.model.vo.activity.ActivityGoodsVO;
import com.voghion.marketing.model.vo.activity.ActivityVO;
import com.voghion.marketing.model.vo.activity.*;
import com.voghion.marketing.mq.MqSender;
import com.voghion.marketing.pojo.BusinessMarketingResultCode;
import com.voghion.marketing.service.*;
import com.voghion.marketing.service.impl.CommonService;
import com.voghion.marketing.util.BeanCopyUtil;
import com.voghion.marketing.utils.ArrayUtils;
import com.voghion.marketing.utils.DateTimeUtil;
import com.voghion.marketing.utils.GoodsDiscountLabelUtil;
import com.voghion.marketing.utils.RetryUtils;
import com.voghion.marketing.vo.bq.OutDbGoodsEveryDayVO;
import com.voghion.product.api.dto.ActivityOriginalPriceDto;
import com.voghion.product.api.dto.ActivityTagGoodsDTO;
import com.voghion.product.api.dto.FaMerchantsApplyDTO;
import com.voghion.product.api.dto.ShopTagDTO;
import com.voghion.product.api.enums.ProductAPIRedisEnums;
import com.voghion.product.api.input.CostPriceDTO;
import com.voghion.product.api.output.*;
import com.voghion.product.api.service.GoodsRemoteService;
import com.voghion.product.model.enums.GoodsLockLabelTypEnums;
import com.voghion.product.model.po.FaMerchantsApply;
import com.voghion.user.remove.dto.UserDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.client.core.CountRequest;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.search.SearchHit;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.voghion.marketing.core.impl.ActivityOriginalPriceCoreServiceImpl.ACTIVITY_GOODS_PRICE_TAG;

@Slf4j
@Service
public class NewActivityGoodsCoreServiceImpl extends CommonService implements NewActivityGoodsCoreService {

    @Resource
    private SpecialActivityGoodsService specialActivityGoodsService;
    @Resource
    private SpecialActivityGoodsCoreService specialActivityGoodsCoreService;
    @Resource
    private NewActivityService activityService;
    @Resource
    private NewActivityConfigService activityConfigService;
    @Resource
    private GoodsClientFactory goodsClientFactory;
    @Resource
    private GoodsLockClientFactory goodsLockClientFactory;
    @Resource
    private CategoryClientFactory categoryClientFactory;
    @Resource
    private BidGoodsTemplateService bidGoodsTemplateService;
    @Resource
    private RedisApi redisApi;
    @Resource
    private GoodsEsService goodsEsService;

    @Resource
    private NewActivityService newActivityService;

    @Resource
    private NewActivityGoodsService newActivityGoodsService;
    @Resource
    private ActivityReductionGoodsService activityReductionGoodsService;

    @Resource
    private CustomListItemsService customListItemsService;


    @Resource
    private CustomListService customListService;

    @Resource
    private VirGoodsItemsSyncRecordService virGoodsItemsSyncRecordService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private ActivityOriginalPriceCoreService activityOriginalPriceCoreService;
    @Resource
    private DingDingCoreService dingDingCoreService;
    @Resource
    private AutoSelectConfigService autoSelectConfigService;
    @Resource
    private AutoSelectCategoryConfigService autoSelectCategoryConfigService;
    @Resource
    private BaseEsQueryServiceImpl baseEsQueryService;
    @Resource
    private ShopActivitySignupLimitService shopActivitySignupLimitService;
    @Resource
    private ActivityShopLimitRecordService activityShopLimitRecordService;
    @Resource
    protected RestHighLevelClient restHighLevelClient;

    @Resource
    private ActivityGoodsSelectCheckExecutor activityGoodsSelectCheckExecutor;

    @Resource
    private ActivityFullReductionService activityFullReductionService;

    @Resource
    private ExportTaskService exportTaskService;

    @Resource
    private BussCommonFactory bussCommonFactory;
    @Resource
    private NewActivityShopLimitRecordService newActivityShopLimitRecordService;
    @Resource
    private BatchProcessService batchProcessService;;

    @Resource(name = "exportPool")
    private Executor executor;

    @Resource
    private ClientInfoSupportService clientInfoSupportService;

    @Resource
    private AvailGoodsCacheService availGoodsCacheService;

    private static final String VIR_ITEMS_SYNC_LOCK = "VIR_ITEMS_SYNC_LOCK_";
    private static final String VIR_ITEMS_REMOVE_LOCK = "VIR_ITEMS_REMOVE_LOCK_";
    private static final String VIR_ITEMS_AUTO_SYNC_LOCK = "VIR_ITEMS_AUTO_SYNC_LOCK_";
    public static final String ACTIVITY_PRICE_RESTORE = "activity_price_restore";
    @Resource
    private MqSender mqSender;
    @Resource
    private FaMerchantsApplyService faMerchantsApplyService;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private ActivityOriginalPriceService activityOriginalPriceService;

    @Resource(name = "activityPool")
    private Executor activityPool;
    private String[] includes = new String[]{"id", "isShow", "country", "categoryId","minPrice","goodsExtConfigModel"};
    @Autowired
    private ActivityGoodsService activityGoodsService;
    @Autowired
    private GoodsTagClientFactory goodsTagClientFactory;
    @Resource
    private GoodsRemoteService goodsRemoteService;

    @Override
    public ActivityGoodsSignUpResultVO signUpByOperator(ActivityGoodsSignUpVo vo, Boolean isCheck) {
        log.info("运营新增活动商品 vo:{}", vo);
        NewActivity activity = activityService.selectById(vo.getActivityId());
        CheckUtils.notNull(activity, MarketingResultCode.ACTIVITY_NOT_EXIST);

        if (StringUtils.isBlank(vo.getGoodsIds())) {
            return null;
        }
        BigDecimal discount;
        if(Objects.nonNull(vo.getDiscount())){
            discount = vo.getDiscount();
        } else {
            discount = BigDecimal.ONE;
        }
        List<Long> goodsIds = Arrays.stream(vo.getGoodsIds().trim().split(","))
                .filter(s -> !s.equals(" ") && StringUtils.isNotBlank(s))
                .map(s -> Long.parseLong(s.trim()))
                .distinct().collect(Collectors.toList());
        List<GoodsESVo> goodsList = goodsEsService.queryGoodsByGoodsIds(goodsIds);
        log.info("signUpByOperator query goods from es: {}", goodsList == null ? null : goodsList.size());
        goodsList = Optional.ofNullable(goodsList)
                .map(list -> list.stream().filter(Objects::nonNull).collect(Collectors.toList()))
                .orElse(new ArrayList<>());
        CheckUtils.isEmpty(goodsList, MarketingResultCode.GOODS_NOT_EXIST);

        ActivityGoodsSignUpResultVO resultVO = null;
        if (isCheck) {
            newActivityGoodsSignUpCheck(goodsIds, activity, goodsList);
        } else {
            // 组装result并剔除报名不成功goodsId
            resultVO = buildActivityGoodsSignUpResult(activity, goodsList, goodsIds);
            if (CollectionUtils.isEmpty(goodsIds)) {
                // 没有报名成功的商品 直接返回结果
                return resultVO;
            }
        }

        Map<Long, GoodsESVo> goodsMap = goodsList.stream().filter(Objects::nonNull).collect(Collectors.toMap(GoodsESVo::getId, Function.identity(), (a, b) -> b));
        List<Long> existGoods = newActivityGoodsService.queryExistGoodsIds(vo.getActivityId(), goodsIds);

        //查询进行中flashDeal活动的商品原价格
        Map<Long, List<ActivityOriginalPriceDto>> startingGoodsPriceMap;
        List<NewActivity> startingActivitys = activityService.queryCurrentNormalActivity();
        if (CollectionUtils.isNotEmpty(startingActivitys)) {
            List<ActivityOriginalPriceDto> startingGoodsPriceInfo = Lists.newArrayList();
            for (NewActivity newActivity : startingActivitys) {
                startingGoodsPriceInfo.addAll(goodsClientFactory.queryStartingActivityGoodsInfo(newActivity.getId(), goodsIds));

            }
            startingGoodsPriceMap = startingGoodsPriceInfo.stream().collect(Collectors.groupingBy(ActivityOriginalPriceDto::getGoodsId));
        } else {
            startingGoodsPriceMap = Maps.newHashMap();
        }

        String userName = vo.getUserName();
        if(StringUtils.isBlank(userName)){
            userName = getUserName();
        }
        List<NewActivityGoods> saveList = new ArrayList<>();
        Date date = new Date();
        for (Long goodsId : goodsIds) {
            GoodsESVo goods = goodsMap.get(goodsId);
            if (!existGoods.contains(goodsId)) {
                NewActivityGoods ag = new NewActivityGoods();
                ag.setActivityId(vo.getActivityId());
                ag.setActivityName(activity.getName());
                ag.setGoodsId(goodsId);
                ag.setGoodsName(goods.getName());
                ag.setCategoryId(goods.getCategoryId());
                ag.setShopId(goods.getShopId());
                ag.setMainImage(goods.getMainImage());
                ag.setShopName(goods.getShopName());
                ag.setDiscount(discount);
                ag.setApplyChannel(1);
                ag.setIsShow(goods.getIsShow());
                ag.setGoodsCreateTime(goods.getCreateTime());

                List<ActivityOriginalPriceDto> activityOriginalPriceDtoList = startingGoodsPriceMap.get(goodsId);
                if (CollectionUtils.isNotEmpty(activityOriginalPriceDtoList)) {
                    activityOriginalPriceDtoList.stream().map(ActivityOriginalPriceDto::getOldPrice).min(BigDecimal::compareTo).ifPresent(ag::setMinPrice);
                    activityOriginalPriceDtoList.stream().map(ActivityOriginalPriceDto::getOldPrice).max(BigDecimal::compareTo).ifPresent(ag::setMaxPrice);
                    activityOriginalPriceDtoList.stream().map(ActivityOriginalPriceDto::getOldPrice).min(BigDecimal::compareTo)
                            .ifPresent(e -> ag.setAfterDiscountMinPrice(e.multiply(discount)));
                    activityOriginalPriceDtoList.stream().map(ActivityOriginalPriceDto::getOldPrice).max(BigDecimal::compareTo)
                            .ifPresent(e -> ag.setAfterDiscountMaxPrice(e.multiply(discount)));

                    List<BigDecimal> oldGroupPriceList = activityOriginalPriceDtoList.stream()
                            .map(ActivityOriginalPriceDto::getOldGrouponPrice).filter(Objects::nonNull).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(oldGroupPriceList)) {
                        oldGroupPriceList.stream().min(BigDecimal::compareTo).ifPresent(ag::setAfterDiscountMinGrouponPrice);
                        oldGroupPriceList.stream().max(BigDecimal::compareTo).ifPresent(ag::setAfterDiscountMaxGrouponPrice);
                    }
                } else {
                    ag.setMinPrice(goods.getMinPrice());
                    ag.setMaxPrice(goods.getMaxPrice());
                    ag.setAfterDiscountMinPrice(goods.getMinPrice().multiply(discount));
                    ag.setAfterDiscountMaxPrice(goods.getMaxPrice().multiply(discount));
                    ag.setAfterDiscountMinGrouponPrice(goods.getMinGrouponPrice());
                    ag.setAfterDiscountMaxGrouponPrice(goods.getMaxGrouponPrice());
                }

                ag.setOperator(userName);
                ag.setCreateTime(date);
                ag.setUpdateTime(date);
                ag.setFirstAuditTime(date);
                ag.setAuditTime(date);
                ag.setFirstAuditor(userName);
                ag.setAuditor(userName);
                ag.setFirstStatus(1);
                ag.setIsBackendUpload(1);

                saveList.add(ag);
            }
        }

        if (CollectionUtils.isNotEmpty(saveList)) {
            List<NewActivityGoods> newActivityGoodsList = newActivityGoodsService.lambdaQuery()
                    .in(NewActivityGoods::getGoodsId, goodsIds)
                    .eq(NewActivityGoods::getActivityId, activity.getId())
                    .list();
            List<Long> existSize = newActivityGoodsList.stream().map(NewActivityGoods::getGoodsId).distinct().collect(Collectors.toList());
            transactionTemplate.execute(status -> {
                try {
                    //补全小二信息
                    completePrincipalInfo(saveList);

                    newActivityGoodsService.insertBatch(saveList);
                    newActivityService.updateNewActivitySignUpNums(activity.getId(), (long) saveList.size() - existSize.size(), DigitalOperationsEnum.ADD);
                    //去掉运营后台上传文件后入选数量
                    //newActivityService.updateNewActivitySelectedNums(activity.getId(), (long) saveList.size(),DigitalOperationsEnum.ADD);
                    if (activity.getType() == 1) {
                        goodsLockClientFactory.addLock(goodsIds, Collections.singletonList(GoodsLockLabelTypEnums.FLASH_DEAL.getCode()));
                    }
                    return true;
                } catch (Exception e) {
                    status.setRollbackOnly();
                    throw new RuntimeException(e);
                }
            });
        }

        return resultVO;
    }

    private ActivityGoodsSignUpResultVO buildActivityGoodsSignUpResult(NewActivity activity, List<GoodsESVo> goodsList, List<Long> goodsIds) {
        ActivityGoodsSignUpResultVO resultVO = new ActivityGoodsSignUpResultVO();
        List<ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult> signUpResults = new ArrayList<>();

        Map<Long, GoodsESVo> goodsMap = goodsList.stream().filter(Objects::nonNull).collect(Collectors.toMap(GoodsESVo::getId, Function.identity(), (a, b) -> b));
        // 检验商品是否存在
        Set<Long> normalList = goodsList.stream().filter(g -> Objects.nonNull(g) && Objects.equals(g.getIsDel(), 0)).map(GoodsESVo::getId).collect(Collectors.toSet());
        if(normalList.size() < goodsIds.size()){
            Set<Long> notFoundSet = goodsIds.stream().filter(g -> !normalList.contains(g)).collect(Collectors.toSet());
            for (Long notExistId : notFoundSet) {
                ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult signUpResult = new ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult();
                signUpResult.setGoodsId(notExistId);
                GoodsESVo goodsESVo = goodsMap.get(notExistId);
                signUpResult.setShopId(goodsESVo != null ? goodsESVo.getShopId() : null);
                signUpResult.setCode(ActivitySignUpResultEnum.NOT_EXIST.getCode());
                signUpResults.add(signUpResult);
            }
            goodsIds.removeAll(notFoundSet);
            goodsList = goodsList.stream().filter(goods -> Objects.nonNull(goods) && !notFoundSet.contains(goods.getId())).collect(Collectors.toList());
        }

        //检验商品是否参加了其他活动
        Map<Long, List<Long>> otherActivityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(goodsList)) {
            List<NewActivityGoods> newActivityGoods = newActivityGoodsService.listJoin(goodsList.stream().map(GoodsESVo::getId).collect(Collectors.toList()));
            if(CollectionUtils.isNotEmpty(newActivityGoods)){
                Set<Long> activityIds = newActivityGoods.stream().map(NewActivityGoods::getActivityId).collect(Collectors.toSet());
                List<NewActivity> newActivities = newActivityService.list(new QueryWrapper<NewActivity>()
                        .in("activity_status", 10, 20, 60, 70)
                        .in("id", new ArrayList<>(activityIds))
                );
                Map<Long, NewActivity> activityMap = newActivities.stream().collect(Collectors.toMap(NewActivity::getId, Function.identity()));
                newActivityGoods.forEach(ag ->{
                    NewActivity ac = activityMap.get(ag.getActivityId());
                    if ((activity.getEffectStartTime().compareTo(ac.getEffectStartTime()) >= 0 && activity.getEffectStartTime().compareTo(ac.getEffectEndTime()) <= 0)
                            || (activity.getEffectEndTime().compareTo(ac.getEffectStartTime()) >= 0 && activity.getEffectEndTime().compareTo(ac.getEffectEndTime()) <= 0)
                            || (ac.getEffectStartTime().compareTo(activity.getEffectStartTime()) >= 0 && ac.getEffectStartTime().compareTo(activity.getEffectEndTime()) <= 0)
                            || (ac.getEffectEndTime().compareTo(activity.getEffectStartTime()) >= 0 && ac.getEffectEndTime().compareTo(activity.getEffectEndTime()) <= 0)
                    ) {
                        otherActivityMap.computeIfAbsent(ag.getGoodsId(), k -> new ArrayList<>()).add(ac.getId());
                    }
                });
            }
        }
        if (MapUtils.isNotEmpty(otherActivityMap)) {
            for (Map.Entry<Long, List<Long>> entry : otherActivityMap.entrySet()) {
                ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult signUpResult = new ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult();
                signUpResult.setGoodsId(entry.getKey());
                GoodsESVo goodsESVo = goodsMap.get(entry.getKey());
                signUpResult.setShopId(goodsESVo != null ? goodsESVo.getShopId() : null);
                signUpResult.setCode(ActivitySignUpResultEnum.PARTICIPATE_OTHER_ACTIVITY.getCode());
                signUpResult.setExtra(StringUtils.join(entry.getValue(), ","));
                signUpResults.add(signUpResult);
            }
            goodsIds.removeAll(otherActivityMap.keySet());
            goodsList = goodsList.stream().filter(goods -> !otherActivityMap.containsKey(goods.getId())).collect(Collectors.toList());
        }

        // 检查商品上架状态
        List<Long> notShowList = goodsList.stream().filter(g -> !Objects.equals("1", g.getIsShow())).map(GoodsESVo::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notShowList)){
            for (Long notShowId : notShowList) {
                ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult signUpResult = new ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult();
                signUpResult.setGoodsId(notShowId);
                GoodsESVo goodsESVo = goodsMap.get(notShowId);
                signUpResult.setShopId(goodsESVo != null ? goodsESVo.getShopId() : null);
                signUpResult.setCode(ActivitySignUpResultEnum.SHELF_OFF.getCode());
                signUpResults.add(signUpResult);
            }
            goodsIds.removeAll(notShowList);
            goodsList = goodsList.stream().filter(goods -> !notShowList.contains(goods.getId())).collect(Collectors.toList());
        }

        // 4、活动允许类目
        Set<Long> allLevelCategoryIds = new HashSet<>();
        if (StringUtils.isNotBlank(activity.getCateCol())) {
            List<Long> categoryIds = Arrays.stream(activity.getCateCol().split(",")).map(Long::parseLong).collect(Collectors.toList());
            allLevelCategoryIds.addAll(categoryIds);
            Map<Long, List<Long>> mapResult = categoryClientFactory.queryChildCategoryByParentIds(categoryIds);
            for (Long categoryId : categoryIds) {
                List<Long> childCategoryIds = mapResult.get(categoryId);
                if (CollectionUtils.isNotEmpty(childCategoryIds)) {
                    allLevelCategoryIds.addAll(childCategoryIds);
                }
            }
        }
        //限定类目改动，支持限定类目和排除类目
        if(CollectionUtils.isNotEmpty(allLevelCategoryIds)){
            List<Long> notContainGoodsIds = null;
            if (activity.getCatType().equals(1)) { //1，指定限定类目，则只有限定类目的商品参与活动
                notContainGoodsIds = goodsList.stream().filter(g -> !allLevelCategoryIds.contains(g.getCategoryId())).map(GoodsESVo::getId).collect(Collectors.toList());
            }
            if (activity.getCatType().equals(2)) { //2，指定排除类目，则排除类目的商品不能参与活动
                notContainGoodsIds = goodsList.stream().filter(g -> allLevelCategoryIds.contains(g.getCategoryId())).map(GoodsESVo::getId).collect(Collectors.toList());
            }

            if(CollectionUtils.isNotEmpty(notContainGoodsIds)){
                for (Long notContainId : notContainGoodsIds) {
                    ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult signUpResult = new ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult();
                    signUpResult.setGoodsId(notContainId);
                    GoodsESVo goodsESVo = goodsMap.get(notContainId);
                    signUpResult.setShopId(goodsESVo != null ? goodsESVo.getShopId() : null);
                    signUpResult.setCode(ActivitySignUpResultEnum.CATEGORY_ERROR.getCode());
                    signUpResults.add(signUpResult);
                }
                List<Long> finalIds = notContainGoodsIds;
                goodsIds.removeAll(finalIds);
                goodsList = goodsList.stream().filter(goods -> !finalIds.contains(goods.getId())).collect(Collectors.toList());
            }
        }

        // 限定商家
        if (activity.getShopLimit() == 1) {
            List<ActivityShopLimitRecord> activityShopLimitRecordList = activityShopLimitRecordService.lambdaQuery()
                    .eq(ActivityShopLimitRecord::getActivityId, activity.getId())
                    .list();
            List<Long> limitShopIds = activityShopLimitRecordList.stream().map(ActivityShopLimitRecord::getShopId).collect(Collectors.toList());
            List<Long> notLimitIds = goodsList.stream().filter(g -> !limitShopIds.contains(g.getShopId())).map(GoodsESVo::getId).collect(Collectors.toList());
            for (Long notLimitId : notLimitIds) {
                ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult signUpResult = new ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult();
                signUpResult.setGoodsId(notLimitId);
                GoodsESVo goodsESVo = goodsMap.get(notLimitId);
                signUpResult.setShopId(goodsESVo != null ? goodsESVo.getShopId() : null);
                signUpResult.setCode(ActivitySignUpResultEnum.MERCHANT_ERROR.getCode());
                signUpResults.add(signUpResult);
            }
            goodsIds.removeAll(notLimitIds);
            goodsList = goodsList.stream().filter(goods -> !notLimitIds.contains(goods.getId())).collect(Collectors.toList());
        }

        //校验是否是自营&负向商品
        Map<Long, Long> goodsShopMap = goodsList.stream().collect(Collectors.toMap(GoodsESVo::getId, GoodsESVo::getShopId, (v1, v2) -> v1));
        List<Long> selfGoodIds = this.checkIsSelfShop(goodsShopMap);
        if (CollectionUtils.isNotEmpty(selfGoodIds)) {
            log.info("The product fails to pass compliance checks goodsIds : {}", selfGoodIds);
            selfGoodIds.forEach(goodId -> {
                ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult signUpResult = new ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult();
                signUpResult.setGoodsId(goodId);
                GoodsESVo goodsESVo = goodsMap.get(goodId);
                signUpResult.setShopId(goodsESVo != null ? goodsESVo.getShopId() : null);
                signUpResult.setCode(ActivitySignUpResultEnum.COMPLIANCE_ERROR.getCode());
                signUpResults.add(signUpResult);
            });
            goodsIds.removeAll(selfGoodIds);
            goodsList = goodsList.stream().filter(goods -> !selfGoodIds.contains(goods.getId())).collect(Collectors.toList());
        }

        // 报名成功
        for (GoodsESVo goodsESVo : goodsList) {
            ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult signUpResult = new ActivityGoodsSignUpResultVO.ActivityGoodsSignUpResult();
            signUpResult.setGoodsId(goodsESVo.getId());
            signUpResult.setShopId(goodsESVo.getShopId());
            signUpResult.setCode(ActivitySignUpResultEnum.SUCCESS.getCode());
            signUpResults.add(signUpResult);
        }
        resultVO.setResultList(signUpResults);
        return resultVO;
    }

    void newActivityGoodsSignUpCheck(List<Long> goodsIds, NewActivity activity, List<GoodsESVo> goodsList){
        // 1、商品是否存在,删除状态
        StringBuilder sb = new StringBuilder();
        Set<Long> normalList = goodsList.stream().filter(g -> Objects.nonNull(g) && Objects.equals(g.getIsDel(), 0)).map(GoodsESVo::getId).collect(Collectors.toSet());
        if(normalList.size() < goodsIds.size()){
            Set<Long> notFoundSet = goodsIds.stream().filter(g -> !normalList.contains(g)).collect(Collectors.toSet());
            sb.append(org.apache.commons.lang3.StringUtils.join(notFoundSet, ",")).append(" ").append(MarketingResultCode.GOODS_NOT_EXIST_EXT.getDetail()).append("\n");
        }

        // 2、已参加活动
        List<NewActivityGoods> newActivityGoods = newActivityGoodsService.listJoin(goodsIds);
        List<Long> otherActivityList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(newActivityGoods)){
            Set<Long> activityIds = newActivityGoods.stream().map(NewActivityGoods::getActivityId).collect(Collectors.toSet());
            List<NewActivity> newActivities = newActivityService.list(new QueryWrapper<NewActivity>()
                    .in("activity_status", 10, 20, 60, 70)
                    .in("id", new ArrayList<>(activityIds))
                    .eq("is_del", 0)
            );
            Map<Long, NewActivity> activityMap = newActivities.stream().collect(Collectors.toMap(NewActivity::getId, Function.identity()));
            newActivityGoods.forEach(ag -> {
                NewActivity ac = activityMap.get(ag.getActivityId());
                // 活动商品可能已经参加的活动结束了 这部分数据可以继续参加活动 但是ac为null
                if (Objects.isNull(ac)) {
                    return;
                }
                if ((activity.getEffectStartTime().compareTo(ac.getEffectStartTime()) >= 0 && activity.getEffectStartTime().compareTo(ac.getEffectEndTime()) <= 0)
                        || (activity.getEffectEndTime().compareTo(ac.getEffectStartTime()) >= 0 && activity.getEffectEndTime().compareTo(ac.getEffectEndTime()) <= 0)
                        || (ac.getEffectStartTime().compareTo(activity.getEffectStartTime()) >= 0 && ac.getEffectStartTime().compareTo(activity.getEffectEndTime()) <= 0)
                        || (ac.getEffectEndTime().compareTo(activity.getEffectStartTime()) >= 0 && ac.getEffectEndTime().compareTo(activity.getEffectEndTime()) <= 0)
                ) {
                    otherActivityList.add(ag.getGoodsId());
                }
            });
        }
        if(CollectionUtils.isNotEmpty(otherActivityList)){
            sb.append(org.apache.commons.lang3.StringUtils.join(otherActivityList, ",")).append(" ").append(MarketingResultCode.EXIST_ACTIVITY_GOODS.getDetail()).append("\n");
        }

        // 3、上架状态
        List<Long> notShowList = goodsList.stream().filter(g -> !Objects.equals("1", g.getIsShow())).map(GoodsESVo::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notShowList)){
            sb.append(org.apache.commons.lang3.StringUtils.join(notShowList, ",")).append(" ").append(MarketingResultCode.GOODS_IS_NOT_SHOW.getDetail()).append("\n");
        }

        // 4、活动允许类目
        Set<Long> allLevelCategoryIds = new HashSet<>();
        if (StringUtils.isNotBlank(activity.getCateCol())) {
            List<Long> categoryIds = Arrays.stream(activity.getCateCol().split(",")).map(Long::parseLong).collect(Collectors.toList());
            allLevelCategoryIds.addAll(categoryIds);
            Map<Long, List<Long>> mapResult = categoryClientFactory.queryChildCategoryByParentIds(categoryIds);
            for (Long cateid : categoryIds) {
                List<Long> categoryVOS = mapResult.get(cateid);
                if(CollectionUtils.isEmpty(categoryVOS)){
                    categoryVOS = new ArrayList<>();
                    categoryVOS.add(cateid);
                }else{
                    categoryVOS.add(cateid);
                }
                allLevelCategoryIds.addAll(categoryVOS);
            }

        }
        //限定类目改动，支持限定类目和排除类目
        if(CollectionUtils.isNotEmpty(allLevelCategoryIds)){
            List<Long> categoryIds = null;
            if (activity.getCatType().equals(1)) { //1，指定限定类目，则只有限定类目的商品参与活动
                categoryIds = goodsList.stream().filter(g -> !allLevelCategoryIds.contains(g.getCategoryId())).map(GoodsESVo::getId).collect(Collectors.toList());
            }
            if (activity.getCatType().equals(2)) { //2，指定排除类目，则排除类目的商品不能参与活动
                categoryIds = goodsList.stream().filter(g -> allLevelCategoryIds.contains(g.getCategoryId())).map(GoodsESVo::getId).collect(Collectors.toList());
            }
            //List<Long> notAllowCategory = goodsList.stream().filter(g -> !allLevelCategoryIds.contains(g.getCategoryId())).map(GoodsESVo::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(categoryIds)) {
                sb.append(StringUtils.join(categoryIds, ",")).append(" ").append(MarketingResultCode.GOODS_CATEGORY_NOT_CONFORM.getDetail()).append("\n");
            }
        }

        //校验是否是自营&负向商品
        Map<Long, Long> goodsShopMap = goodsList.stream().collect(Collectors.toMap(GoodsESVo::getId, GoodsESVo::getShopId, (v1, v2) -> v1));
        List<Long> selfGoodIds = this.checkIsSelfShop(goodsShopMap);
        if (CollectionUtils.isNotEmpty(selfGoodIds)) {
            sb.append(CustomResultCode.fill(MarketingResultCode.GOODS_COMPLIANCE_ERROR, StringUtils.join(selfGoodIds, ",")).getMsg()).append("\n");
        }

        callError(sb.toString());
    }

    void callError(String errorInfo) {
        CheckUtils.check(org.apache.commons.lang3.StringUtils.isNotBlank(errorInfo),
                new CustomResultCode("custom", errorInfo, errorInfo)
        );
    }

    @Override
    public Boolean signUpByShopKeeper(ActivityGoodsSignUpVo vo) {
        log.info("商家报名活动商品 vo:{}", vo);
        List<Long> goodsIds = Arrays.stream(vo.getGoodsIds().trim().split("\\n"))
                .filter(s -> !s.equals(" ") && StringUtils.isNotBlank(s))
                .map(s -> Long.parseLong(s.trim()))
                .distinct()
                .collect(Collectors.toList());
        List<ActivityGoodsSignUpByImportVo> importVo = goodsIds.stream()
                .map(goodsId -> new ActivityGoodsSignUpByImportVo(goodsId, vo.getDiscount()))
                .collect(Collectors.toList());
        batchSighUpByTemplate(vo.getActivityId(), importVo);
        return Boolean.TRUE;
    }

    void signCheck(){
        checkConfig();
        checkDiscount();
        checkSignNUm();
        checkGoods();
        checkDuplicateActivity();
        checkBlack();
        callNewActivityError();
    }

    void callNewActivityError(){
        StringBuilder noticeDesc = ThreadContext.getContext("noticeDesc");
        callError(noticeDesc.toString());
    }

    void checkConfig(){
        NewActivity activity = ThreadContext.getContext("activity");
        CheckUtils.notNull(activity, MarketingResultCode.ACTIVITY_NOT_EXIST);
        Long shopId = getShopId();
        CheckUtils.notNull(shopId, MarketingResultCode.NOT_FIND_USER_INFO);
        if(Objects.equals(activity.getShopLimit(), 1)){
            List<ActivityShopLimitRecord> activityShopLimitRecordList = activityShopLimitRecordService.lambdaQuery()
                    .eq(ActivityShopLimitRecord::getShopId, shopId)
                    .eq(ActivityShopLimitRecord::getActivityId, activity.getId()).list();
            CheckUtils.check(CollectionUtils.isEmpty(activityShopLimitRecordList), MarketingResultCode.ACTIVITY_NOT_EXIST);
        }
        //校验当前是否在报名日期内
        Date now = new Date();
        CheckUtils.check(now.before(activity.getSignStartTime()) || now.after(activity.getSignEndTime()), MarketingResultCode.NOT_IN_SIGN_TIME);
    }



    void checkDiscount(){
        NewActivity activity = ThreadContext.getContext("activity");
        if (activity.getType() == 3) {
            return;
        }
        StringBuilder noticeDesc = ThreadContext.getContext("noticeDesc");
        List<ActivityGoodsSignUpByImportVo> goodsInfoList = ThreadContext.getContext("importVoList");
        if(goodsInfoList.stream().anyMatch(vo -> vo.getDiscount().compareTo(activity.getLowestDiscount()) > 0)){
            noticeDesc.append(MarketingResultCode.BIGGER_THAN_ACTIVITY_MIN_DISCOUNT.getMsg()).append("\n");
        }
    }
    void checkSignNUm(){
        NewActivity activity = ThreadContext.getContext("activity");
        Long shopId = ThreadContext.getContext("shopId");
        StringBuilder noticeDesc = ThreadContext.getContext("noticeDesc");
        List<ActivityGoodsSignUpByImportVo> goodsInfoList = ThreadContext.getContext("importVoList");
        int existGoodsNum = newActivityGoodsService.countCanSignUp(activity.getId(), shopId);
        NewActivityShopLimitRecord limitRecord = newActivityShopLimitRecordService.lambdaQuery()
                .eq(NewActivityShopLimitRecord::getShopId, shopId)
                .eq(NewActivityShopLimitRecord::getActivityId, activity.getId()).one();
        Long signUpTotal = activity.getSignUpTotal();
        if(Objects.nonNull(limitRecord)){
            signUpTotal = (long)limitRecord.getLimitNum();
        }
        if(goodsInfoList.size() + existGoodsNum > signUpTotal)
            noticeDesc.append(MarketingResultCode.GOODS_COUNT_OVER_LIMIT.getMsg()).append("\n");
    }
    void checkGoods(){
        Long shopId = ThreadContext.getContext("shopId");
        NewActivity activity = ThreadContext.getContext("activity");
        StringBuilder noticeDesc = ThreadContext.getContext("noticeDesc");
        List<Long> goodsIds = ThreadContext.getContext("goodsIds");
        List<GoodsESVo> goodsList = goodsEsService.queryGoodsByGoodsIds(goodsIds);
        List<Long> existGoodsIds = org.apache.commons.collections4.CollectionUtils.emptyIfNull(goodsList).stream()
                .filter(g -> Objects.nonNull(g) && 0 == g.getIsDel())
                .map(GoodsESVo::getId)
                .collect(Collectors.toList());

        if (existGoodsIds.size() < goodsIds.size()) {
            goodsIds.removeAll(existGoodsIds);
            noticeDesc.append(CustomResultCode.fill(MarketingResultCode.GOODS_NOT_EXIST_EXT, StringUtils.join(goodsIds, ",")).getMsg()).append("\n");
        }

        Set<Long> allLevelCategoryIds = new HashSet<>();
        if (StringUtils.isNotBlank(activity.getCateCol())) {
            List<Long> categoryIds = Arrays.stream(activity.getCateCol().split(",")).map(Long::parseLong).collect(Collectors.toList());
            allLevelCategoryIds.addAll(categoryIds);
            Map<Long, List<Long>> mapResult = categoryClientFactory.queryChildCategoryByParentIds(categoryIds);
            for (Long cateid : categoryIds) {
                List<Long> categoryVOS = mapResult.get(cateid);
                if(CollectionUtils.isEmpty(categoryVOS)){
                    categoryVOS = new ArrayList<>();
                }
                categoryVOS.add(cateid);
                allLevelCategoryIds.addAll(categoryVOS);
            }
        }
        if(CollectionUtils.isNotEmpty(allLevelCategoryIds)){

            List<Long> categoryIds = null;
            if (activity.getCatType().equals(1)) { //1，指定限定类目，则只有限定类目的商品参与活动
                categoryIds = goodsList.stream().filter(g -> !allLevelCategoryIds.contains(g.getCategoryId())).map(GoodsESVo::getId).collect(Collectors.toList());
            }
            if (activity.getCatType().equals(2)) { //2，指定排除类目，则排除类目的商品不能参与活动
                categoryIds = goodsList.stream().filter(g -> allLevelCategoryIds.contains(g.getCategoryId())).map(GoodsESVo::getId).collect(Collectors.toList());
            }

            //List<Long> a = goodsList.stream().filter(g -> !allLevelCategoryIds.contains(g.getCategoryId())).map(GoodsESVo::getId).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(categoryIds))
                noticeDesc.append(CustomResultCode.fill(MarketingResultCode.GOODS_CATEGORY_NOT_CONFORM, StringUtils.join(categoryIds, ",")).getMsg()).append("\n");
        }
        List<Long> b = goodsList.stream().filter(g -> !Objects.equals(shopId, g.getShopId())).map(GoodsESVo::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(b)){
            noticeDesc.append(CustomResultCode.fill(MarketingResultCode.EXIST_GOODS_NOT_BELONG_SHOP, StringUtils.join(b, ",")).getMsg()).append("\n");
        }
        List<Long> d = goodsList.stream().filter(g -> !Objects.equals(g.getIsShow(), "1")).map(GoodsESVo::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(d)){
            noticeDesc.append(CustomResultCode.fill(MarketingResultCode.GOODS_IS_NOT_SHOW, StringUtils.join(d, ",")).getMsg()).append("\n");
        }
        List<Long> e = goodsList.stream().filter(g -> g.getItemList().stream().noneMatch(sku -> sku.getStock() > 0)).map(GoodsESVo::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(e)){
            noticeDesc.append(CustomResultCode.fill(MarketingResultCode.GOODS_SKU_MUST_EXIST_STOCK, StringUtils.join(e, ",")).getMsg()).append("\n");
        }

        //校验是否是自营&负向商品
        Map<Long, Long> goodsShopMap = goodsList.stream().collect(Collectors.toMap(GoodsESVo::getId, GoodsESVo::getShopId, (v1, v2) -> v1));
        List<Long> selfGoodIds = this.checkIsSelfShop(goodsShopMap);
        if (CollectionUtils.isNotEmpty(selfGoodIds)) {
            noticeDesc.append(CustomResultCode.fill(MarketingResultCode.GOODS_COMPLIANCE_ERROR, StringUtils.join(selfGoodIds, ",")).getMsg()).append("\n");
        }
    }

    void checkDuplicateActivity(){
        List<Long> goodsIds = ThreadContext.getContext("goodsIds");
        List<NewActivityGoods> newActivityGoods = newActivityGoodsService.listJoinV2(goodsIds);
        log.info("checkDuplicateActivity newActivityGoods {}", newActivityGoods);
        Set<Long> activityIds = newActivityGoods.stream().map(NewActivityGoods::getActivityId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(newActivityGoods)) return;

        StringBuilder noticeDesc = ThreadContext.getContext("noticeDesc");
        NewActivity activity = ThreadContext.getContext("activity");
        if (activity.getType() == 3) {
            return;
        }
        List<NewActivity> newActivities = newActivityService.lambdaQuery()
                .in(NewActivity::getActivityStatus, Arrays.asList(10, 20, 60, 70))
                .in(NewActivity::getId, activityIds)
                .eq(NewActivity::getIsDel, 0)
                .list();
        log.info("checkDuplicateActivity newActivities {}", newActivities);
        Map<Long, List<Long>> goodsMap = newActivityGoods.stream().collect(Collectors.groupingBy(NewActivityGoods::getActivityId, Collectors.mapping(NewActivityGoods::getGoodsId, Collectors.toList())));
        StringBuilder dupString = new StringBuilder();
        for (NewActivity ac : newActivities) {
            if ((activity.getEffectStartTime().compareTo(ac.getEffectStartTime()) >= 0 && activity.getEffectStartTime().compareTo(ac.getEffectEndTime()) <= 0)
                    || (activity.getEffectEndTime().compareTo(ac.getEffectStartTime()) >= 0 && activity.getEffectEndTime().compareTo(ac.getEffectEndTime()) <= 0)
                    || (ac.getEffectStartTime().compareTo(activity.getEffectStartTime()) >= 0 && ac.getEffectStartTime().compareTo(activity.getEffectEndTime()) <= 0)
                    || (ac.getEffectEndTime().compareTo(activity.getEffectStartTime()) >= 0 && ac.getEffectEndTime().compareTo(activity.getEffectEndTime()) <= 0)
            ) {
                List<Long> exsitGoodList = goodsMap.get(ac.getId());
                if(CollectionUtils.isNotEmpty(exsitGoodList)){
                    dupString.append(CustomResultCode.fill(MarketingResultCode.DUP_ACTIVITY_GOODS, new Object[]{StringUtils.join(exsitGoodList, ","), ac.getName()}).getMsg());
                }
                log.info("checkDuplicateActivity exist GoodList {}", exsitGoodList);
            }
        }
        if(dupString.length() > 0){
            noticeDesc.append(dupString).append("\n");
        }
    }

    void checkBlack(){
        List<ActivityGoodsSignUpByImportVo> goodsInfoList = ThreadContext.getContext("importVoList");
        StringBuilder noticeDesc = ThreadContext.getContext("noticeDesc");
        List<Long> goodsIds = goodsInfoList.stream().map(ActivityGoodsSignUpByImportVo::getGoodsId).collect(Collectors.toList());
        List<Long> blacklistedGoods = goodsClientFactory.blacklistedGoods(goodsIds);
        if(CollectionUtils.isNotEmpty(blacklistedGoods)){
            noticeDesc.append(CustomResultCode.fill(MarketingResultCode.BLACK_LIST_GOODS_FORBID_SIGN_ACTIVITY, StringUtils.join(blacklistedGoods, ",")).getMsg()).append("\n");
        }
    }

    void signBefore(Long activityId, List<ActivityGoodsSignUpByImportVo> list){
        NewActivity activity = activityService.selectById(activityId);
        List<Long> goodsIds = list.stream().map(ActivityGoodsSignUpByImportVo::getGoodsId).distinct().collect(Collectors.toList());
        List<GoodsESVo> goodsList = goodsEsService.queryGoodsByGoodsIds(goodsIds);
        StringBuilder noticeDesc = new StringBuilder();
        ThreadContext.getContext().put("activity", activity);
        ThreadContext.getContext().put("noticeDesc", noticeDesc);
        ThreadContext.getContext().put("goodsList", goodsList);
        ThreadContext.getContext().put("importVoList", list);
        ThreadContext.getContext().put("shopId", getShopId());
        ThreadContext.getContext().put("goodsIds", goodsIds);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSighUpByTemplate(Long activityId, List<ActivityGoodsSignUpByImportVo> list) {
        log.info("shop batchSighUpByTemplate {} {}", activityId, JSON.toJSONString(list));
        if(CollectionUtils.isEmpty(list) || Objects.isNull(activityId)) return;
        try {
            signBefore(activityId, list);
            signCheck();
            updateDel();
            batchSign();
        }finally {
            clear();
        }
    }

    void updateDel(){
        NewActivity activity = ThreadContext.getContext("activity");
        List<Long> goodsIds = ThreadContext.getContext("goodsIds");
        newActivityGoodsService.lambdaUpdate()
                .set(NewActivityGoods::getIsDel, 1)
                .set(NewActivityGoods::getUpdateTime, new Date())
                .eq(NewActivityGoods::getActivityId, activity.getId())
                .in(NewActivityGoods::getGoodsId, goodsIds)
                .update();
    }
    void batchSign(){
        NewActivity activity = ThreadContext.getContext("activity");
        List<Long> goodsIds = ThreadContext.getContext("goodsIds");
        List<GoodsESVo> goodsList = ThreadContext.getContext("goodsList");
        List<ActivityGoodsSignUpByImportVo> importVoList = ThreadContext.getContext("importVoList");
        String userName = getUserName();
        Map<Long, List<ActivityOriginalPriceDto>> effectGoodsMap = getEffectGoods(goodsIds);
        Map<Long, BigDecimal> discountMap = importVoList.stream().collect(Collectors.toMap(ActivityGoodsSignUpByImportVo::getGoodsId, activityGoodsSignUpByImportVo -> Optional.ofNullable(activityGoodsSignUpByImportVo.getDiscount()).orElse(BigDecimal.ONE), (a, b) -> a));
        Map<Long, GoodsESVo> goodsMap = goodsList.stream().collect(Collectors.toMap(GoodsESVo::getId, Function.identity(), (a, b) -> b));
        List<NewActivityGoods> activityGoods = goodsIds.stream()
                .filter(g -> Objects.nonNull(goodsMap.get(g)) && Objects.nonNull(discountMap.get(g)))
                .map(goodsId -> {
                    NewActivityGoods g = new NewActivityGoods();
                    GoodsESVo goods = goodsMap.get(goodsId);
                    g.setActivityId(activity.getId());
                    g.setActivityName(activity.getName());
                    g.setGoodsId(goodsId);
                    g.setDiscount(discountMap.get(goodsId));
                    g.setGoodsName(goods.getName());
                    g.setShopId(goods.getShopId());
                    g.setShopName(goods.getShopName());
                    g.setMainImage(goods.getMainImage());
                    g.setCategoryId(goods.getCategoryId());
                    g.setIsShow(goods.getIsShow());
                    g.setGoodsCreateTime(goods.getCreateTime());
                    g.setOperator(userName);
                    g.setCreateTime(new Date());
                    g.setUpdateTime(new Date());
                    g.setStatus(-1);
                    g.setApplyChannel(0);
                    setPrice(g, goods, effectGoodsMap.get(g.getGoodsId()));
                    return g;
                }).collect(Collectors.toList());

        List<NewActivityGoods> newActivityGoodsList = newActivityGoodsService.lambdaQuery()
                .in(NewActivityGoods::getGoodsId, goodsIds)
                .eq(NewActivityGoods::getActivityId, activity.getId())
                .list();
        List<Long> existSize = newActivityGoodsList.stream().map(NewActivityGoods::getGoodsId).distinct().collect(Collectors.toList());

        //补全小二信息
        completePrincipalInfo(activityGoods);

        newActivityGoodsService.insertBatch(activityGoods);
        newActivityService.updateNewActivitySignUpNums(activity.getId(), (long) activityGoods.size() - existSize.size(),DigitalOperationsEnum.ADD);
        if (activity.getType() == 1) {
            goodsLockClientFactory.addLock(goodsIds, Collections.singletonList(GoodsLockLabelTypEnums.FLASH_DEAL.getCode()));
        }
    }
    void setPrice(NewActivityGoods g, GoodsESVo goods, List<ActivityOriginalPriceDto> activityOriginalPriceDtoList){
        if (CollectionUtils.isNotEmpty(activityOriginalPriceDtoList)) {
            activityOriginalPriceDtoList.stream().map(ActivityOriginalPriceDto::getOldPrice).min(BigDecimal::compareTo)
                    .ifPresent(g::setMinPrice);
            activityOriginalPriceDtoList.stream().map(ActivityOriginalPriceDto::getOldPrice).max(BigDecimal::compareTo)
                    .ifPresent(g::setMaxPrice);
            activityOriginalPriceDtoList.stream().map(ActivityOriginalPriceDto::getOldPrice).min(BigDecimal::compareTo)
                    .ifPresent(minPrice -> g.setAfterDiscountMinPrice(minPrice.multiply(g.getDiscount()).setScale(2, BigDecimal.ROUND_UP)));
            activityOriginalPriceDtoList.stream().map(ActivityOriginalPriceDto::getOldPrice).max(BigDecimal::compareTo)
                    .ifPresent(maxPrice -> g.setAfterDiscountMaxPrice(maxPrice.multiply(g.getDiscount()).setScale(2, BigDecimal.ROUND_UP)));
            List<BigDecimal> oldGroupPriceList = activityOriginalPriceDtoList.stream()
                    .map(ActivityOriginalPriceDto::getOldGrouponPrice).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(oldGroupPriceList)) {
                oldGroupPriceList.stream().min(BigDecimal::compareTo).ifPresent(minGrouponPrice ->
                        g.setAfterDiscountMinGrouponPrice(minGrouponPrice.multiply(g.getDiscount()).setScale(2, BigDecimal.ROUND_UP)));
                oldGroupPriceList.stream().max(BigDecimal::compareTo).ifPresent(minGrouponPrice ->
                        g.setAfterDiscountMaxGrouponPrice(minGrouponPrice.multiply(g.getDiscount()).setScale(2, BigDecimal.ROUND_UP)));
            }
        } else {
            g.setMinPrice(goods.getMinPrice());
            g.setMaxPrice(goods.getMaxPrice());
            g.setAfterDiscountMinPrice(goods.getMinPrice().multiply(g.getDiscount()).setScale(2, BigDecimal.ROUND_UP));
            g.setAfterDiscountMaxPrice(goods.getMaxPrice().multiply(g.getDiscount()).setScale(2, BigDecimal.ROUND_UP));
            if (Objects.nonNull(goods.getMinGrouponPrice()) && Objects.nonNull(goods.getMaxGrouponPrice())) {
                g.setAfterDiscountMinGrouponPrice(goods.getMinGrouponPrice().multiply(g.getDiscount()).setScale(2, BigDecimal.ROUND_UP));
                g.setAfterDiscountMaxGrouponPrice(goods.getMaxGrouponPrice().multiply(g.getDiscount()).setScale(2, BigDecimal.ROUND_UP));
            }
        }
    }
    void clear(){
        ThreadContext.clean();
    }

    Map<Long, List<ActivityOriginalPriceDto>> getEffectGoods(List<Long> goodsIds){
        Map<Long, List<ActivityOriginalPriceDto>> startingGoodsPriceMap = Maps.newHashMap();
        List<NewActivity> newActivities = activityService.queryCurrentNormalActivity();
        if(CollectionUtils.isNotEmpty(newActivities)){
            for(NewActivity newActivity : newActivities){
                List<ActivityOriginalPriceDto> startingGoodsPriceInfo = goodsClientFactory.queryStartingActivityGoodsInfo(newActivity.getId(), Lists.newArrayList(goodsIds));
                startingGoodsPriceMap.putAll(startingGoodsPriceInfo.stream().collect(Collectors.groupingBy(ActivityOriginalPriceDto::getGoodsId)));
            }
        }
        return startingGoodsPriceMap;
    }
    void checkGoodsOtherActivityEffect(List<Long> goodsIds, NewActivity activity){
        Set<Long> activityIds = newActivityGoodsService.queryActivityIdsByGoodsIds(goodsIds);
        if(CollectionUtils.isNotEmpty(activityIds)){
            List<NewActivity> newActivities = newActivityService.list(new QueryWrapper<NewActivity>()
                    .in("activity_status", 10, 20, 60, 70)
                    .in("id", new ArrayList<>(activityIds))
                    .eq("is_del", 0)
            );
            newActivities.forEach(ac ->{
                if ((activity.getEffectStartTime().compareTo(ac.getEffectStartTime()) >= 0 && activity.getEffectStartTime().compareTo(ac.getEffectEndTime()) <= 0)
                        || (activity.getEffectEndTime().compareTo(ac.getEffectStartTime()) >= 0 && activity.getEffectEndTime().compareTo(ac.getEffectEndTime()) <= 0)
                        || (ac.getEffectStartTime().compareTo(activity.getEffectStartTime()) >= 0 && ac.getEffectStartTime().compareTo(activity.getEffectEndTime()) <= 0)
                        || (ac.getEffectEndTime().compareTo(activity.getEffectStartTime()) >= 0 && ac.getEffectEndTime().compareTo(activity.getEffectEndTime()) <= 0)
                ) {
                    CheckUtils.check(true, MarketingResultCode.EXIST_ACTIVITY_GOODS);
                }
            });
        }
    }


    public void setAutoSelect(List<NewActivityGoods> activityGoods, Long activityId, AutoSelectConfig autoSelectConfig, Set<Long> whiteShopIdSetFromShopTag) {
        if (CollectionUtils.isEmpty(activityGoods) || Objects.isNull(activityId)) {
            log.info("setAutoSelect activityGoods empty or activityid is null");
            return;
        }

        Set<Long> shopWiteList = null;
        if (StringUtils.isNotBlank(autoSelectConfig.getWhiteShopList())) {
            shopWiteList = Arrays.stream(autoSelectConfig.getWhiteShopList().split(","))
                    .filter(s -> !s.equals(" ") && StringUtils.isNotBlank(s))
                    .map(s -> Long.parseLong(s.trim()))
                    .collect(Collectors.toSet());
        }

        // 1、校验商品数
        checkNum(activityGoods, autoSelectConfig, activityId);
        List<AutoSelectCategoryConfig> autoSelectCategoryConfigList = autoSelectCategoryConfigService.lambdaQuery()
                .eq(AutoSelectCategoryConfig::getSelectId, autoSelectConfig.getId())
                .list();
        Map<Long, BigDecimal> categoryAmountMap = org.apache.commons.collections4.CollectionUtils.emptyIfNull(autoSelectCategoryConfigList).stream()
                .collect(Collectors.toMap(AutoSelectCategoryConfig::getCategoryId, AutoSelectCategoryConfig::getMaxAmount, (a, b) -> a));

        Map<Long, String> pidsMap = getParentIdMap(autoSelectCategoryConfigList, activityGoods);
        Map<Long, GoodsExtConfigModel> goodsExtConfigModelMap = ggetGodsExtConfigModelMap(autoSelectConfig, activityGoods);

        List<Long> goodsIds = activityGoods.stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toList());

        ActivityGoodsSelectCheckParam checkParam = new ActivityGoodsSelectCheckParam();
        checkParam.setAutoSelectConfig(autoSelectConfig);
        checkParam.setCategoryAmountMap(categoryAmountMap);
        checkParam.setPidsMap(pidsMap);
        checkParam.setGoodsExtConfigModelMap(goodsExtConfigModelMap);
        List<GoodsESVo> goodsESVos = goodsEsService.queryGoodsByGoodsIds(goodsIds);
        checkParam.setGoodsEsVoMap(goodsESVos.stream().collect(Collectors.toMap(GoodsESVo::getId, Function.identity())));

        int selectedNum = 0;
        for (NewActivityGoods goods : activityGoods) {
            //白名单商家 && 白名单商家店铺id
            if ( (shopWiteList != null && shopWiteList.contains(goods.getShopId()))
                    || (CollectionUtils.isNotEmpty(whiteShopIdSetFromShopTag) && whiteShopIdSetFromShopTag.contains(goods.getShopId())) ) {
                goods.setFirstStatus(1);
                goods.setStatus(1);
                goods.setAuditTime(new Date());
                goods.setFirstAuditRemark("白名单商家初审系统自动通过");
                goods.setAuditRemark("白名单商家终审系统自动通过");
                goods.setAutoSelectStatus(1);
                goods.setAutoSelectFailReason(null);
                selectedNum++;
                continue;
            }

            if (Objects.equals(goods.getAutoSelectStatus(), 2)) { //审核不通过
                continue;
            }
            goods.setAutoSelectStatus(1);
            ActivityGoods activityGoods1 = new ActivityGoods();
            activityGoods1.setActivityType(1);
            activityGoods1.setActivityId(activityId);
            activityGoods1.setGoodsName(goods.getGoodsName());
            activityGoods1.setGoodsId(goods.getGoodsId());
            activityGoods1.setPrice(goods.getAfterDiscountMaxPrice());
            activityGoods1.setCategoryId(goods.getCategoryId());
            activityGoods1.setDiscount(goods.getDiscount());
            activityGoods1.setImageUrl(goods.getMainImage());
            checkParam.setActivityGoods(activityGoods1);


            // 机审检查，添加异常处理以避免中断整个循环
            try {
                ActivityFirstSelectDTO selectFail = activityGoodsSelectCheckExecutor.check(checkParam);
                if (Objects.nonNull(selectFail)) {
                    setFailReason(goods, selectFail);
                }
            } catch (Exception e) {
                log.error("setAutoSelect activityId : {} goodsId {}", activityId, goods.getGoodsId(), e);
                continue;
            }


            //后台运营上传入选商品 && 机审通过
            if (Objects.equals(goods.getIsBackendUpload(), 1) && Objects.equals(goods.getAutoSelectStatus(), 1)) {
                goods.setStatus(1);
                goods.setAuditRemark("后台运营选品上传系统自动通过");
                goods.setAuditTime(new Date());
                selectedNum++;
            }
            //自动审核都补全机审时间
            goods.setAutoSelectTime(LocalDateTime.now());
        }
        log.info("setAutoSelect activityId : {} activityGoods.size = {}", activityId, activityGoods.size());
        newActivityGoodsService.updateBatchById(activityGoods);

        //自动审核需要加上入选数量
        if (selectedNum > 0) {
            newActivityService.updateNewActivitySelectedNums(activityId, (long) selectedNum, DigitalOperationsEnum.ADD);
        }
    }
    Map<Long, GoodsExtConfigModel> ggetGodsExtConfigModelMap(AutoSelectConfig autoSelectConfig, List<NewActivityGoods> activityGoods){
        Map<Long, GoodsExtConfigModel> goodsExtConfigModelMap = new HashMap<>();
        if(Objects.nonNull(autoSelectConfig.getIsNegative())){
            List<GoodsESVo> goodsESVos = goodsEsService.queryGoodsByGoodsIds(activityGoods.stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toList()));
            goodsExtConfigModelMap = org.apache.commons.collections4.CollectionUtils.emptyIfNull(goodsESVos).stream()
                    .filter(e -> Objects.nonNull(e) && Objects.nonNull(e.getGoodsExtConfigModel()))
                    .collect(Collectors.toMap(GoodsESVo::getId, GoodsESVo::getGoodsExtConfigModel, (existing, replacement) -> existing));
        }
        return goodsExtConfigModelMap;
    }

    Map<Long, String> getParentIdMap(List<AutoSelectCategoryConfig> autoSelectCategoryConfigList, List<NewActivityGoods> activityGoods){
        Map<Long, String> pidsMap = new HashMap<>();
        if(CollectionUtils.isEmpty(autoSelectCategoryConfigList)) return pidsMap;
        Set<Long> categoryIdSet = activityGoods.stream().filter(t -> Objects.nonNull(t.getCategoryId())).map(NewActivityGoods::getCategoryId).collect(Collectors.toSet());
        List<CategoryVO> categoryVOList = categoryClientFactory.queryCategoryByIds(new ArrayList<>(categoryIdSet)).getData();
        pidsMap = categoryVOList.stream().collect(Collectors.toMap(CategoryVO::getId, e -> {
            String pids = e.getPids();
            if (StringUtils.isBlank(pids)) return String.valueOf(e.getId());
            String[] split = pids.split(",");
            List<String> list = Arrays.asList(split);
            Collections.reverse(list);
            return e.getId() + "," + org.apache.commons.lang3.StringUtils.join(list, ",");
        }));
        log.info("setAutoSelect parentIdMap = {}", JSON.toJSONString(pidsMap));
        return pidsMap;
    }
    private void setFailReason(NewActivityGoods goods, ActivityFirstSelectDTO activityFirstSelectDTO){
        Date now = new Date();

        if (Objects.isNull(goods.getFirstStatus()) || !Objects.equals(goods.getFirstStatus(), 1)) {
            goods.setFirstStatus(0);
        }

        goods.setFirstAuditor("机审");
        ActivityFirstSelectFail selectFail = activityFirstSelectDTO.getFirstSelectFail();
        if(selectFail == ActivityFirstSelectFail.IS_NEGATIVE){
            goods.setFirstAuditRemark("商品可能涉及侵权，限制提报活动");
        }else{
            goods.setFirstAuditRemark(selectFail.getDesc());
        }
        goods.setFirstAuditTime(now);
        goods.setAutoSelectStatus(2);
        if(selectFail == ActivityFirstSelectFail.SAME_TOP){
            goods.setAutoSelectFailReason("存在同款更低商品价格");
        }else{
            goods.setAutoSelectFailReason(selectFail.getDesc());
        }

        if (selectFail == ActivityFirstSelectFail.AVER_PRICE && StringUtils.isNotBlank(selectFail.getDesc()) &&
                StringUtils.isNotBlank(activityFirstSelectDTO.getDiscountAvePrice())) {
            String reason = String.format(selectFail.getDesc(), activityFirstSelectDTO.getDesc()) + "(参考均价: " + activityFirstSelectDTO.getDiscountAvePrice() + ")";
            goods.setAutoSelectFailReason(reason);
            goods.setFirstAuditRemark(reason);
        }

        goods.setAutoSelectTime(LocalDateTime.now());
    }

    private void checkNum(List<NewActivityGoods> activityGoods, AutoSelectConfig autoSelectConfig, Long activityId){
        Date now = new Date();

        /**
         * 1、 取出上面两个审核通过的
         * 2、根据订单量和加购量排序，截取当前的可报名数量
         * 3、当前的可报名数量为   min(total - 已报名num, 动销个数 * (num + 1) + 额外num(当前未必用上)  + 上次剩余num)
         */
        if(needCheckRunDaysSales(autoSelectConfig)){

            // --todo 考虑下为空情况
//            List<NewActivityGoods> filterList = activityGoods.stream().filter(e -> Objects.equals(e.getAutoSelectStatus(), 1)).collect(Collectors.toList());
            List<Long> goodsIdList = activityGoods.stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toList());
            List<OutDbGoodsEveryDayVO> goodsEveryDayVOList = getOutDbGoodsEveryDayList(goodsIdList, autoSelectConfig.getRunDays());
            Map<Long, OutDbGoodsEveryDayVO> salesMap = goodsEveryDayVOList.stream().collect(Collectors.toMap(OutDbGoodsEveryDayVO::getGoodsId, Function.identity()));
            log.info("setAutoSelect needCheckRunDaysSales activityGoods ={}", JSON.toJSONString(activityGoods));

            // 本次可以添加的商品数量（动销 + 非动销）
            Map<Long, Long> canAddShopMap = org.apache.commons.collections4.CollectionUtils.emptyIfNull(goodsEveryDayVOList).stream()
                    .filter(e -> Objects.nonNull(e.getShopId()))
                    .filter(e -> Objects.nonNull(e.getDealCnt()) && e.getDealCnt() >= autoSelectConfig.getSales())
                    .collect(Collectors.groupingBy(OutDbGoodsEveryDayVO::getShopId, Collectors.counting()));

            log.info("setAutoSelect canAddShopMap ={}", canAddShopMap);

            // 商家活动已报名数量表
            Set<Long> shopIdSet = activityGoods.stream().map(NewActivityGoods::getShopId).collect(Collectors.toSet());
            List<ShopActivitySignupLimit> shopActivitySignupLimitList = getShopActivitySignupLimitList(shopIdSet, activityId, autoSelectConfig);
            Map<Long, ShopActivitySignupLimit> shopActivitySignupLimitMap = shopActivitySignupLimitList.stream().collect(Collectors.toMap(ShopActivitySignupLimit::getShopId, Function.identity()));

            NewActivity newActivity = newActivityService.getById(activityId);
            Long total = newActivity.getSignUpTotal();
            Map<Long, List<NewActivityGoods>> shopGoodsMap = activityGoods.stream().collect(Collectors.groupingBy(NewActivityGoods::getShopId));
            LocalDateTime localDateTimeNow = LocalDateTime.now();
            log.info("setAutoSelect salesMap ={}", JSON.toJSONString(salesMap));
            shopGoodsMap.forEach((shopId, goodsList) ->{

                // 排序
                sortBySales(goodsList, salesMap);
                log.info("setAutoSelect sort after goodsList ={}", JSON.toJSONString(goodsList));
                ShopActivitySignupLimit shopActivitySignupLimit = shopActivitySignupLimitMap.get(shopId);
                Long curSalesCount = canAddShopMap.getOrDefault(shopId, 0L);
                //获取可报名数量 剩余可以报名总数 或者店铺可以报名商品数 取最小值
                int cur = (int)Math.min(Math.max(total - shopActivitySignupLimit.getJoinNum(), 0), curSalesCount + shopActivitySignupLimit.getLastRemain());
                int overLimitRemainChange = 0, lastRemainChange = 0;
                int t = cur;
                if(cur < goodsList.size()){
//                    cur = Math.min(goodsList.size(), cur + shopActivitySignupLimit.getOverLimitRemain());
                    cur = Math.min(goodsList.size(), cur);
                    overLimitRemainChange = t - cur;
                    lastRemainChange = -shopActivitySignupLimit.getLastRemain();
                }else{
                    cur = goodsList.size();
                    lastRemainChange = t - cur;
                }
                log.info("setAutoSelect overLimitRemainChange ={}, lastRemainChange ={}, cur = {}", overLimitRemainChange, lastRemainChange, cur);
//                shopActivitySignupLimit.setOverLimitRemain(shopActivitySignupLimit.getOverLimitRemain() + overLimitRemainChange);
                shopActivitySignupLimit.setLastRemain(shopActivitySignupLimit.getLastRemain() + lastRemainChange);
                shopActivitySignupLimit.setJoinNum(shopActivitySignupLimit.getJoinNum() + cur);

                if(cur < goodsIdList.size()){
                    List<NewActivityGoods> failList = goodsList.subList(cur, goodsList.size());

                    Set<Long> fitGoodsIdSet = goodsEveryDayVOList.stream()
                            .filter(e -> Objects.nonNull(e.getDealCnt()) && e.getDealCnt() >= autoSelectConfig.getSales())
                            .map(OutDbGoodsEveryDayVO::getGoodsId).collect(Collectors.toSet());
                    failList.forEach(goods ->{
                        String failDes = "";
                        if(fitGoodsIdSet.contains(goods.getGoodsId())){
                            failDes = "可报名商品数已超出";
                        }else{
                            failDes = getRunDaysFailDes(autoSelectConfig);
                        }
                        goods.setFirstStatus(0);
                        goods.setFirstAuditor("机审");
                        goods.setFirstAuditRemark(failDes);
                        goods.setFirstAuditTime(now);
                        goods.setAutoSelectStatus(2);
                        goods.setAutoSelectFailReason(failDes);
                        goods.setAutoSelectTime(localDateTimeNow);
                    });
                    log.info("setAutoSelect check sales failList = {}", JSON.toJSONString(failList));
                }

            });
            shopActivitySignupLimitService.updateBatchById(shopActivitySignupLimitList);
        }
    }

    void sortBySales(List<NewActivityGoods> list, Map<Long, OutDbGoodsEveryDayVO> salesMap){
        list.sort(
                Comparator.comparing((NewActivityGoods goods) -> {
                            OutDbGoodsEveryDayVO outDbGoodsEveryDayVO = salesMap.get(goods.getGoodsId());
                            if (Objects.isNull(outDbGoodsEveryDayVO)) {
                                return 0L;
                            }
                            return Objects.isNull(outDbGoodsEveryDayVO.getDealCnt()) ? 0L : outDbGoodsEveryDayVO.getDealCnt();
                        },Comparator.reverseOrder())
                        .thenComparing((NewActivityGoods goods) -> {
                            OutDbGoodsEveryDayVO outDbGoodsEveryDayVO = salesMap.get(goods.getGoodsId());
                            if (Objects.isNull(outDbGoodsEveryDayVO)) {
                                return 0L;
                            }
                            return Objects.isNull(outDbGoodsEveryDayVO.getAddToCartConfirmGoodsPv()) ? 0L : outDbGoodsEveryDayVO.getAddToCartConfirmGoodsPv();
                        },Comparator.reverseOrder())
        );
    }


    @Override
    public void handSelect(Long activityId){
        try {
            log.info("handSelect activityId : {}", activityId);
            NewActivity activity = activityService.getById(activityId);
            if (Objects.isNull(activity)) {
                log.warn("handSelect activity is null, activityId = {}", activityId);
                return;
            }
            AutoSelectConfig autoSelectConfig = autoSelectConfigService.lambdaQuery()
                    .eq(AutoSelectConfig::getType, activity.getType())
                    .eq(AutoSelectConfig::getTypeId, activityId).one();
            if (Objects.isNull(autoSelectConfig)) {
                log.info("setAutoSelect autoSelectConfig is null, activityId = {}", activityId);
                return;
            }
            Set<Long> whiteShopIdSetFromShopTag = getWhiteShopIdSetFromShopTag(autoSelectConfig);

            int pageNow = 1, pageSize = 1000;
            IPage<NewActivityGoods> page;
            do{
                page = newActivityGoodsService.lambdaQuery()
                        .eq(NewActivityGoods::getActivityId, activityId)
                        .eq(NewActivityGoods::getIsDel, 0)
                        .eq(NewActivityGoods::getAutoSelectStatus, 0)
                        .orderByAsc(NewActivityGoods::getShopId)
                        .page(new Page<>(pageNow++, pageSize));
                if(CollectionUtils.isEmpty(page.getRecords())){
                    log.info("setAutoSelect activity   = {}, pageNow = {}", activityId, pageNow);
                    return;
                }
                setAutoSelect(page.getRecords(), activityId, autoSelectConfig, whiteShopIdSetFromShopTag);
            }while (CollectionUtils.isNotEmpty(page.getRecords()));

        }catch (Throwable e){
            log.info("setAutoSelect fail, activity id = {}, message = {}, e = {}", activityId, e.getMessage(), e.getStackTrace());
        }
    }

    // 处理shopTagId 到shop的转化
    private Set<Long> getWhiteShopIdSetFromShopTag(AutoSelectConfig autoSelectConfig) {
        Set<Long> whiteShopIdSetFromShopTag = Sets.newHashSet();
        if (StringUtils.isNotBlank(autoSelectConfig.getWhiteShopTagList())){
            List<Long> tagIds = Arrays.stream(autoSelectConfig.getWhiteShopTagList().split(","))
                    .filter(s -> !s.equals(" ") && StringUtils.isNotBlank(s))
                    .map(s -> {
                        try{
                            return Long.valueOf(s.trim());
                        }catch (Exception ignored){}
                        return null;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
            List<ShopTagDTO> shopTagDTOS = goodsTagClientFactory.queryShopTagsByIds(tagIds);
            whiteShopIdSetFromShopTag = shopTagDTOS.stream().flatMap(e-> e.getShopIdList().stream()).collect(Collectors.toSet());
            log.info("newActivity actId: {}, type:{}, whiteShopIdSetFromShopTag = {}", autoSelectConfig.getTypeId(), autoSelectConfig.getType(), whiteShopIdSetFromShopTag);
        }
        return whiteShopIdSetFromShopTag;
    }

    @Override
    public void setAutoSelect() {
        log.info("setAutoSelect start");
        long start = System.currentTimeMillis();
        List<NewActivity> newActivities = newActivityService.listAllSignUp();

        newActivities.forEach(activity -> {

          try {
            //1 - flashdeal 2 - 运营选品 4满减 todo 枚举不一致 需要整理刷数据
            Integer configType = Objects.equals(1, activity.getType()) ? activity.getType() : 2;
            AutoSelectConfig autoSelectConfig = autoSelectConfigService.lambdaQuery()
                    .eq(AutoSelectConfig::getType, configType)
                    .eq(AutoSelectConfig::getTypeId, activity.getId()).one();
            if (Objects.isNull(autoSelectConfig)) {
              log.info("setAutoSelect autoSelectConfig is null, activityId = {}", activity.getId());
              return;
            }
            Set<Long> whiteShopIdSetFromShopTag = getWhiteShopIdSetFromShopTag(autoSelectConfig);
            int pageNow = 1, pageSize = 1000;
                IPage<NewActivityGoods> page;
                Long lastId = 0L;
                do {
                    page = newActivityGoodsService.lambdaQuery()
                            .eq(NewActivityGoods::getActivityId, activity.getId())
                            .eq(NewActivityGoods::getIsDel, 0)
                            .eq(NewActivityGoods::getAutoSelectStatus, 0)
                            .orderByAsc(NewActivityGoods::getId)
                            .gt(NewActivityGoods::getId, lastId)
                            .page(new Page<>(pageNow, pageSize));
                    if (CollectionUtils.isEmpty(page.getRecords())) {
                        log.info("setAutoSelect activity   = {}, pageNow = {}", activity, pageNow);
                        return;
                    }
                    setAutoSelect(page.getRecords(), activity.getId(), autoSelectConfig, whiteShopIdSetFromShopTag);
                    log.info("setAutoSelect activityId : {} pageNow : {} success", activity.getId(), pageNow);
                    lastId = page.getRecords().get(page.getRecords().size() - 1).getId();
                } while (true);

            } catch (Throwable e) {
                log.info("setAutoSelect fail, activity id = {}, message = {}, e = {}", activity.getId(), e.getMessage(), e.getStackTrace());
            }
        });
        log.info("setAutoSelect newActivities = {}, end, cost = {}", JSON.toJSONString(newActivities), System.currentTimeMillis() - start);
    }

    List<ShopActivitySignupLimit> getShopActivitySignupLimitList(Set<Long> shopIdSet, Long activityId, AutoSelectConfig autoSelectConfig){
        List<ShopActivitySignupLimit> shopActivitySignupLimitList = shopActivitySignupLimitService.lambdaQuery()
                .eq(ShopActivitySignupLimit::getActivityId, activityId)
                .eq(ShopActivitySignupLimit::getActivityType, autoSelectConfig.getType())
                .in(ShopActivitySignupLimit::getShopId, shopIdSet)
                .list();

        // 不存在先创建
        if(!Objects.equals(shopActivitySignupLimitList.size(), shopIdSet.size())){
            Set<Long> existShopIdSet = shopActivitySignupLimitList.stream().map(ShopActivitySignupLimit::getShopId).collect(Collectors.toSet());
            List<ShopActivitySignupLimit> needInsetList = shopIdSet.stream()
                    .filter(shopId -> !existShopIdSet.contains(shopId))
                    .map(shopId -> {
                        ShopActivitySignupLimit shopActivitySignupLimit = new ShopActivitySignupLimit();
                        shopActivitySignupLimit.setShopId(shopId);
                        shopActivitySignupLimit.setActivityId(activityId);
                        shopActivitySignupLimit.setJoinNum(0);
                        shopActivitySignupLimit.setLastRemain(0);
//                        shopActivitySignupLimit.setOverLimitRemain(autoSelectConfig.getOverLimitNum());
                        return shopActivitySignupLimit;
                    }).collect(Collectors.toList());
            shopActivitySignupLimitService.insertBatch(needInsetList);
            shopActivitySignupLimitList.addAll(needInsetList);
        }
        return shopActivitySignupLimitList;
    }
    List<OutDbGoodsEveryDayVO> getOutDbGoodsEveryDayList(List<Long> goodsIdList, Integer runDays){
        SearchRequest source = new SearchRequest("goods_every_day_es_a")
                .source(new SearchSourceBuilder().query(new BoolQueryBuilder()
                                .must(QueryBuilders.termsQuery("goodsId", goodsIdList))
                                .must(QueryBuilders.termQuery("runDays", runDays))
                        )
                        .size(goodsIdList.size()));
        List<OutDbGoodsEveryDayVO> goodsEveryDayVOList = baseEsQueryService.searchData(source, OutDbGoodsEveryDayVO.class);
        log.info("setAutoSelect goodsIdList = {}, runDays ={}, goodsEveryDayVOList = {}", runDays, JSON.toJSONString(goodsIdList), JSON.toJSONString(goodsEveryDayVOList));
        return goodsEveryDayVOList;
    }
    String getRunDaysFailDes(AutoSelectConfig autoSelectConfig){
        return "在" + autoSelectConfig.getRunDays() + "天内，销量小于" + autoSelectConfig.getSales() + "单";
    }

    boolean needCheckRunDaysSales(AutoSelectConfig autoSelectConfig){
        return Objects.nonNull(autoSelectConfig.getRunDays()) && Objects.nonNull(autoSelectConfig.getSales());
    }

    private Long getSameGoodsNameLowestPriceGoods(NewActivityGoods goods, Integer activityType, Long activityId){
        String goodsName = goods.getGoodsName();
        Long goodsId = goods.getGoodsId();
        String sameGoodsNameKey = getSameGoodsNameKey(activityType, activityId);
        Object hget = redisApi.hget(sameGoodsNameKey, goodsName);
        if(Objects.isNull(hget)){
            GoodsVO goodsVO = new GoodsVO();
            goodsVO.setGoodId(goodsId);
            goodsVO.setAfterDiscountMaxPrice(goods.getAfterDiscountMaxPrice());
            redisApi.hset(sameGoodsNameKey, goodsName, JSON.toJSONString(goodsVO), 30 * 24 * 60 * 60);
            return goodsId;
        }
        GoodsVO existVO = JSON.parseObject(hget.toString(), GoodsVO.class);
        if(Objects.equals(goodsId, existVO.getGoodId())){
            return goodsId;
        }
        if(existVO.getAfterDiscountMaxPrice().compareTo(goods.getAfterDiscountMaxPrice()) <= 0){
            return existVO.getGoodId();
        }
        GoodsVO newGoodsVO = new GoodsVO();
        newGoodsVO.setGoodId(goodsId);
        newGoodsVO.setAfterDiscountMaxPrice(goods.getAfterDiscountMaxPrice());
        redisApi.hset(sameGoodsNameKey, goodsName, JSON.toJSONString(newGoodsVO), 30 * 24 * 60 * 60);

        // 更新存在的
        try {
            NewActivityGoods newActivityGoods = newActivityGoodsService.lambdaQuery()
                    .eq(NewActivityGoods::getGoodsId, existVO.getGoodId())
                    .eq(NewActivityGoods::getActivityId, activityId).one();
            if(Objects.nonNull(newActivityGoods)){
                newActivityGoods.setAutoSelectStatus(2);
                newActivityGoods.setAutoSelectFailReason("相同商品名称价格不是最低");
                newActivityGoods.setAutoSelectTime(LocalDateTime.now());
                newActivityGoodsService.updateById(newActivityGoods);
            }
        }catch (Exception e) {
            log.info("getSameGoodsNameLowestPriceGoods update activity goods fail, activityId = {}, goodsId = {}, message = {}, e = {}.",
                    activityId, existVO.getGoodId(), e.getMessage(), e);
        }

        return goodsId;
    }
    private String getSameGoodsNameKey(Integer activityType, Long activityId){
        return activityType.toString() + "_" + activityId.toString();
    }


    @Override
    public List<NewActivityGoodsDataDTO> queryActivityGoodsData(NewActivityGoodsDataDTO vo) {
        List<NewActivityGoodsDataDTO> list = new ArrayList<>();
        Map<Long, String> goodsMap;

        //是否活动 ，图片，活动截止时间 ，配置名称，商品id。
        List<Long> goodsIds = vo.getGoodsIds();
        if (CollectionUtils.isEmpty(goodsIds)) {
            return list;
        }
        List<GoodsFieldVo> goods = goodsClientFactory.queryGoodsFieldByIds(goodsIds);
        goodsMap = goods.stream().collect(Collectors.toMap(GoodsFieldVo::getId, GoodsFieldVo::getMainImage));

        List<NewActivity> newActivities = activityService.queryCurrentNormalActivity();
        if(CollectionUtils.isEmpty(newActivities)) return list;

        for(NewActivity newActivity : newActivities){
            List<Long> goodsIdInActivity = newActivityGoodsService.queryExistGoodsIds(newActivity.getId(), goodsIds);
            if(CollectionUtils.isEmpty(goodsIdInActivity)) continue;
            for (Long id : goodsIdInActivity) {
                NewActivityGoodsDataDTO dto = new NewActivityGoodsDataDTO();
                dto.setGoodsId(id);
                if (goodsIdInActivity.contains(id)) {
                    dto.setInActivity(goodsIdInActivity.contains(id) ? 1 : 0);
                    dto.setEffectEndTime(newActivity.getEffectEndTime());
                    NewActivityConfig activityConfig = newActivity.getNewActivityConfig().get(0);
                    if (null != activityConfig) {
                        dto.setDetailImg(activityConfig.getDetailImg());
                        dto.setConfigName(activityConfig.getName());
                    }
                }
                dto.setMainImage(goodsMap.get(id));
                list.add(dto);
            }
        }
        return list;
    }


    @Override
    public void importActivityGoodsSort(Long activityId, List<ActivityGoodsSortVo> list) {
        log.info("商家导入活动商品排序 activityId:{}, list:{}", activityId, JSON.toJSONString(list));

        NewActivity activity = activityService.selectById(activityId);
        CheckUtils.notNull(activity, MarketingResultCode.ACTIVITY_NOT_EXIST);

        NewActivityGoods ex = new NewActivityGoods();
        ex.setActivityId(activityId);
        ex.setStatus(1);
        List<NewActivityGoods> activityGoods = newActivityGoodsService.selectList(ex);

        Set<Long> importGoodsIds = list.stream().map(ActivityGoodsSortVo::getGoodsId).collect(Collectors.toSet());
        List<Long> activitySelectedGoodsIds = activityGoods.stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toList());
        importGoodsIds.retainAll(activitySelectedGoodsIds);

        List<NewActivityGoods> updateList = new ArrayList<>();
        Map<Long, Integer> scoreMap = list.stream().collect(Collectors.toMap(ActivityGoodsSortVo::getGoodsId, ActivityGoodsSortVo::getScore, (a, b) -> a));
        Map<Long, NewActivityGoods> existGoodsMap = activityGoods.stream().collect(Collectors.toMap(NewActivityGoods::getGoodsId, Function.identity(), (a, b) -> b));
        if (CollectionUtils.isNotEmpty(importGoodsIds)) {
            for (Long goodsId : importGoodsIds) {
                NewActivityGoods goods = existGoodsMap.get(goodsId);
                goods.setSort(scoreMap.get(goodsId));
                goods.setOperator(getUserName());
                goods.setUpdateTime(new Date());
                updateList.add(goods);
            }
            newActivityGoodsService.updateBatchById(updateList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean selectOrLostActivityGoods(ActivityGoodsSelectVo vo) {
        CheckUtils.notNull(vo, MarketingResultCode.PARAMETER_ERROR);
        log.info("终审信息 运营入选/落选活动商品 vo:{}", JSON.toJSONString(vo));
        if (CollectionUtils.isEmpty(vo.getIds()) || (0 != vo.getSelect() && 1 != vo.getSelect())) {
            CheckUtils.check(true, MarketingResultCode.PARAMETER_ERROR);
        }
        // 从db中获取商品good数据
        List<NewActivityGoods> newActivityGoodsFromDB = newActivityGoodsService.lambdaQuery()
                .in(NewActivityGoods::getId, vo.getIds())
                .list();
        if (CollectionUtils.isEmpty(newActivityGoodsFromDB)){
            return true;
        }
        // 查看当前商品是否存在于其他
        if(Objects.equals(vo.getSelect(), 1)){
            List<Long> ids = vo.getIds();
            checkPassDuplicate(ids);
        }
        BaseTokenUserInfo userInfo = getUserInfo();
        List<NewActivityGoods> activityGoodsList = new ArrayList<>();
        for (NewActivityGoods  good : newActivityGoodsFromDB) {
            Date date = new Date();
            NewActivityGoods activityGoods = new NewActivityGoods();
            activityGoods.setId(good.getId());
            activityGoods.setStatus(vo.getSelect());
            activityGoods.setAuditTime(date);
            if (null != userInfo) {
                activityGoods.setAuditor(userInfo.getUserName());
            }
            activityGoods.setUpdateTime(date);
            if (0 == vo.getSelect()) {
                activityGoods.setAuditRemark(vo.getRemark());
            }
            activityGoodsList.add(activityGoods);
        }
        if ( 0 == vo.getSelect() && CollectionUtils.isNotEmpty(newActivityGoodsFromDB)) {
            List<Long> needRemoveLockGoodsIds = newActivityGoodsFromDB.stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toList());
            goodsLockClientFactory.removeLock(needRemoveLockGoodsIds, Collections.singletonList(GoodsLockLabelTypEnums.FLASH_DEAL.getCode()));
        }
        //   //更新activity的select nums
        if (0 == vo.getSelect()){
            List<NewActivityGoods> filteredNewActivityGoods = newActivityGoodsFromDB
                    .stream()
                    .filter(newActivityGoods -> newActivityGoods.getStatus() == 1)
                    .collect(Collectors.toList());
            Map<Long, List<NewActivityGoods>> newActivityGoodsMap = filteredNewActivityGoods.stream().collect(Collectors.groupingBy(NewActivityGoods::getActivityId));
            newActivityGoodsMap.forEach((k,v) ->{
                newActivityService.updateNewActivitySelectedNums(k, (long) v.size(),DigitalOperationsEnum.SUBTRACT);
            });
            removeEffectActivityGoods(vo.getIds());
        }else if (1 == vo.getSelect()){
            List<NewActivityGoods> filteredNewActivityGoods = newActivityGoodsFromDB
                    .stream()

                    .filter(newActivityGoods -> newActivityGoods.getStatus()  != 1)
                    .collect(Collectors.toList());
            Map<Long, List<NewActivityGoods>> newActivityGoodsMap = filteredNewActivityGoods.stream().collect(Collectors.groupingBy(NewActivityGoods::getActivityId));
            newActivityGoodsMap.forEach((k,v) ->{
                newActivityService.updateNewActivitySelectedNums(k, (long) v.size(),DigitalOperationsEnum.ADD);
            });
        }
        boolean b = newActivityGoodsService.updateBatchById(activityGoodsList);
        CheckUtils.check(!b, MarketingResultCode.UPDATE_AUDIT_FAILED2);
        log.info("终审信息 更新终审状态成功");
        return b;
    }
    void checkPassDuplicate(List<Long> ids){
        //当前活动下商品列表
        Collection<NewActivityGoods> idDataList = newActivityGoodsService.selectByIds(ids);
        //所有商品
        List<Long> goodsIdList = idDataList.stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toList());
        //获取所有终审核通过的活动商品
        List<NewActivityGoods> newActivityGoodsList = newActivityGoodsService.lambdaQuery()
                .in(NewActivityGoods::getGoodsId, goodsIdList)
                .eq(NewActivityGoods::getIsDel, 0)
                .eq(NewActivityGoods::getStatus, 1)
                .list();

        if(CollectionUtils.isNotEmpty(newActivityGoodsList)){
            List<Long> activityIdList = idDataList.stream().map(NewActivityGoods::getActivityId).collect(Collectors.toList());
            List<Long> collect = newActivityGoodsList.stream().map(NewActivityGoods::getActivityId).collect(Collectors.toList());
            activityIdList.addAll(collect);

            //获取所有的活动
            List<NewActivity> activityFullReductionList = newActivityService.lambdaQuery()
                    .in(NewActivity::getId, activityIdList)
                    .eq(NewActivity::getIsDel, 0)
                    .in(NewActivity::getActivityStatus, 10, 20, 60, 70)
                    .gt(NewActivity::getEffectEndTime, LocalDateTime.now())
                    .list();
            Map<Long, NewActivity> newActivityMap = activityFullReductionList.stream().collect(Collectors.toMap(NewActivity::getId, Function.identity()));

            StringBuilder dupString = new StringBuilder();
            //包括之前活动下的商品列表
            for (NewActivityGoods newActivityGoods : newActivityGoodsList) {
                NewActivity ac = newActivityMap.get(newActivityGoods.getActivityId());
                Long goodsId = newActivityGoods.getGoodsId();
                //当前活动下面的商品
                for(NewActivityGoods newActivityGoods1 : idDataList){
                    if(!Objects.equals(goodsId, newActivityGoods1.getGoodsId())) continue;
                    NewActivity activity = newActivityMap.get(newActivityGoods1.getActivityId());
                    if(Objects.isNull(ac) || Objects.isNull(activity)) continue;
                    if ((activity.getEffectStartTime().compareTo(ac.getEffectStartTime()) >= 0 && activity.getEffectStartTime().compareTo(ac.getEffectEndTime()) <= 0)
                            || (activity.getEffectEndTime().compareTo(ac.getEffectStartTime()) >= 0 && activity.getEffectEndTime().compareTo(ac.getEffectEndTime()) <= 0)
                            || (ac.getEffectStartTime().compareTo(activity.getEffectStartTime()) >= 0 && ac.getEffectStartTime().compareTo(activity.getEffectEndTime()) <= 0)
                            || (ac.getEffectEndTime().compareTo(activity.getEffectStartTime()) >= 0 && ac.getEffectEndTime().compareTo(activity.getEffectEndTime()) <= 0)
                    ) {
                        dupString.append(CustomResultCode.fill(MarketingResultCode.DUP_PASS_GOODS, new Object[]{newActivityGoods.getGoodsId(), ac.getId()}).getMsg());
                    }
                }
            }
            CheckUtils.check(org.apache.commons.lang3.StringUtils.isNotBlank(dupString.toString()),
                    new CustomResultCode("custom", dupString.toString(), dupString.toString())
            );

        }
    }


    @Override
    public NewHomeActivityDataDTO queryHomeActivityData(NewActivityGoodsDataDTO dto) {
        log.info("queryHomeActivityData requestParam:{}", JSON.toJSONString(dto));
        NewActivity activity = activityService.queryHomeActivity(dto);
        if (null == activity) {
            return null;
        }
        String country = getCountry();
        if (2 == activity.getType() && StringUtils.isNotBlank(activity.getCountry()) && !activity.getCountry().contains(country)) {
            //七日达国家配置校验
            log.info("七日达活动未配置该国家:{}", getCountry());
            return null;
        }
        NewActivityConfig activityConfig = null;
        //七日达附属活动，判断活动生效国家是否包含请求头中的国家，取第一个符合条件
        List<NewActivityConfig> configList = activity.getNewActivityConfig();
        if (2 == activity.getType()) {
            for (NewActivityConfig config : configList) {
                if (StringUtils.isNotBlank(config.getCountry()) && config.getCountry().contains(country)) {
                    activityConfig = config;
                    break;
                }
            }
        } else if (1 == activity.getType()) {
            activityConfig = configList.get(0);
        }

        if (null == activityConfig || 1 != activityConfig.getIsShow()) {
            log.info("七日达活动配置为null或不展示:{}", JSON.toJSONString(activityConfig));
            return null;
        }
        return assemble(activityConfig, activity);
    }

    private NewHomeActivityDataDTO assemble(NewActivityConfig activityConfig, NewActivity activity) {
        NewHomeActivityDataDTO dataDTO = BeanCopyUtil.transform(activity, NewHomeActivityDataDTO.class);
        dataDTO.setActivityId(activity.getId());
        dataDTO.setEndDate(activity.getEffectEndTime());
        dataDTO.setName(activityConfig.getName());
        dataDTO.setConfigId(activityConfig.getId());
        dataDTO.setBannerImg(activityConfig.getBannerImg());
        dataDTO.setDetailImg(activityConfig.getDetailImg());
        dataDTO.setHomePageImg(activityConfig.getHomePageImg());
        dataDTO.setIsShow(activityConfig.getIsShow());
        dataDTO.setTagUrl(activityConfig.getTagUrl());
        dataDTO.setType(activity.getType());

        List<FrontCategoryDataDTO> list = new ArrayList<>();
        FrontCategoryDataDTO all = new FrontCategoryDataDTO();
        all.setFrontCateId(0L);
        all.setFrontCateName("all");
        list.add(all);
        if (StringUtils.isNotBlank(activityConfig.getFrontCateCol())) {
            List<Long> frontCategoryIds = Stream.of(activityConfig.getFrontCateCol().split(",")).map(Long::parseLong).collect(Collectors.toList());
            //todo 业务逻辑写在provider端 排序   whd clear
            Map<Long, List<FrontCategoryVO>> mapResult = new HashMap<>();
            List<Long> frontCategoriesSearch = new ArrayList<>();
            for (Long frontCategoryId : frontCategoryIds) {
                Object object = redisApi.get(ProductAPIRedisEnums.FRONT_CATEGORY_KEY.getCode() + frontCategoryId);
                if (object == null) {
                    frontCategoriesSearch.add(frontCategoryId);
                } else {
                    FrontCategoryDTO frontCategory = JSON.parseObject(String.valueOf(object), FrontCategoryDTO.class);
                    FrontCategoryDataDTO frontCategoryDataDTO = new FrontCategoryDataDTO();
                    frontCategoryDataDTO.setFrontCateId(frontCategory.getId());
                    frontCategoryDataDTO.setFrontCateName(frontCategory.getName());
                    list.add(frontCategoryDataDTO);
                }
            }
            if (CollectionUtils.isNotEmpty(frontCategoriesSearch)) {
                List<FrontCategoryDTO> frontCategoryDTOS = categoryClientFactory.queryFrontCategorysByIds(frontCategoriesSearch);
                for (FrontCategoryDTO frontCategory : frontCategoryDTOS) {
                    FrontCategoryDataDTO frontCategoryDataDTO = new FrontCategoryDataDTO();
                    frontCategoryDataDTO.setFrontCateId(frontCategory.getId());
                    frontCategoryDataDTO.setFrontCateName(frontCategory.getName());
                    list.add(frontCategoryDataDTO);
                }
            }

            list.sort(Comparator.comparingInt(o -> frontCategoryIds.indexOf(o.getFrontCateId())));
        }
        dataDTO.setFrontCateList(list);
        log.info("类目顺序{}", JSON.toJSONString(list));

        List<Long> collect;
        if (2 == activity.getType()) {
            collect = specialActivityGoodsCoreService.queryHomeActivityGoodsIds();
        } else {
            collect = newActivityGoodsService.queryHomeActivityGoodsIds(activity.getId());
        }
        if (CollectionUtils.isNotEmpty(collect)) {
            //从es 查询国家商品价格信息
            List<NewGoodsOnlyDTO> goodsInfo = getGoodsInfo(collect);
            dataDTO.setGoodsMsg(goodsInfo);
        }
        log.info("queryHomeActivityData return:{}", JSON.toJSONString(dataDTO));
        return dataDTO;
    }

    @Override
    public PageView<NewGoodsOnlyDTO> queryGoodsByOption(NewActivityGoodsDataDTO dto) {
        log.info("queryGoodsByOption param:{}", JSON.toJSONString(dto));
        //前台类目id 如果为 空 或者为 0 为查询所有 其他则需要先查询对应的后台类目再查询 活动商品
        if (dto == null) {
            dto = new NewActivityGoodsDataDTO();
            dto.setType(1);
        }

        PageView<NewGoodsOnlyDTO> pageView = new PageView<>(dto.getPageSize(), dto.getPageNow());
        List<Long> collect = new ArrayList<>();

        NewActivity activity = activityService.queryHomeActivity(dto);
        if (null == activity) {
            return pageView;
        }
        if (null != activity.getType() && 2 == activity.getType()) {
            SpecialActivityGoodsVO specialActivityGoodsVO = new SpecialActivityGoodsVO();
            specialActivityGoodsVO.setPageNow(dto.getPageNow());
            specialActivityGoodsVO.setPageSize(dto.getPageSize());
            specialActivityGoodsVO.setType(2);
            if (dto.getFrontCategoryId() != null) {
                List<Long> categoryIds = categoryClientFactory.queryChildCategoryIdByFrontCateId(dto.getFrontCategoryId()).getData();
                specialActivityGoodsVO.setCategoryIds(categoryIds);
            }
            collect = specialActivityGoodsService.querySpecialGoodsIdsByPage(specialActivityGoodsVO);
            if (dto.getGoodsId() != null) {
                List<SpecialActivityGoods> res = specialActivityGoodsService.querySpecialGoodsByIdAndType(dto.getGoodsId(), dto.getActivityId());
                if (CollectionUtils.isNotEmpty(res)) {
                    if (CollectionUtils.isNotEmpty(collect)) {
                        if (collect.contains(dto.getGoodsId())) {
                            collect.remove(dto.getGoodsId());
                        }
                        collect.add(0, dto.getGoodsId());
                        if (collect.size() > dto.getPageSize()) {
                            collect = collect.subList(0, dto.getPageSize());
                        }
                    } else {
                        collect.add(dto.getGoodsId());
                    }
                }
            }
        } else {
            CheckUtils.notNull(activity, MarketingResultCode.ACTIVITY_NOT_EXIST);
            PageView<NewActivityGoods> view = null;
            ActivityGoodsVO vo = new ActivityGoodsVO(activity.getId(), 1, dto.getPageNow(), dto.getPageSize(), true);
            final Long frontCategoryId = dto.getFrontCategoryId();
            if (null != frontCategoryId && 0 != frontCategoryId) {

                List<Long> searchCategoryIds = new ArrayList<>();
                Object object = redisApi.get(ProductAPIRedisEnums.CHILD_CATEGORY_ID_BY_CATE.getCode() + frontCategoryId);
                if (object != null) {
                    searchCategoryIds = JSON.parseArray(String.valueOf(object), Long.class);
                } else {
                    searchCategoryIds = categoryClientFactory.queryChildCategoryIdByFrontCateId(frontCategoryId).getData();
                }
                vo.setSearchCategoryIds(Lists.newArrayList(searchCategoryIds));
            }
            view = newActivityGoodsService.queryActivityGoodsByPage(vo);
            if (view == null || CollectionUtils.isEmpty(view.getRecords())) {
                collect = new ArrayList<>();
            } else {
                collect = view.getRecords().stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toList());
            }

            if (dto.getGoodsId() != null && activity.getId()!=null) {
                List<NewActivityGoods> res = newActivityGoodsService.queryByActivityIdAndGoodsId(activity.getId(), Arrays.asList(dto.getGoodsId()));
                if (CollectionUtils.isNotEmpty(res)) {
                    if (CollectionUtils.isNotEmpty(collect)) {
                        if (collect.contains(dto.getGoodsId())) {
                            collect.remove(dto.getGoodsId());
                        }
                        collect.add(0, dto.getGoodsId());
                        if (collect.size() > dto.getPageSize()) {
                            collect = collect.subList(0, dto.getPageSize());
                        }
                    } else {
                        collect.add(dto.getGoodsId());
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(collect)) {
            return new PageView<>(dto.getPageSize(), dto.getPageNow());
        }
        log.info("queryGoodsByOption查询商品国家价格信息前 goodsId:{}", collect);
        //从es 查询商品国家价格信息
        List<NewGoodsOnlyDTO> goodsInfo = getGoodsInfo(collect);
        log.info("queryGoodsByOption查询商品国家价格信息后goodsInfo:{}", JSON.toJSONString(goodsInfo));
        pageView.setRecords(goodsInfo);
        pageView.setPageNow(dto.getPageNow());
        pageView.setPageSize(dto.getPageSize());
        return pageView;
    }


    @Override
    public PageView<ActivityGoodsResult> queryActivityGoodsResult(ActivityGoodsVO vo) {
        if (null == vo) {
            vo = new ActivityGoodsVO();
        }
        log.info("queryActivityGoodsResult param:{}", JSON.toJSONString(vo));
        PageView<ActivityGoodsResult> result = new PageView<>(vo.getPageSize(), vo.getPageNow());

        CheckUtils.check(null == vo.getActivityId()
                        && StringUtils.isBlank(vo.getGoodsIds())
                        && null == vo.getSignUpStatus()
                , MarketingResultCode.ACTIVITY_GOODS_SHOP_ERROR);


        //查询所有的在报名中的 活动id
        if (null == vo.getActivityId() || vo.getActivityId() <= 0) {
            List<NewActivity> newActivities = queryActivityId(vo);
            if (CollectionUtils.isEmpty(newActivities)) {
                return result;
            }
        }


        List<Integer> goodsIdList = vo.getGoodsIdList();
        if(CollectionUtils.isEmpty(goodsIdList)){
            goodsIdList = new ArrayList<>();
        }
        String goodsIdsStr = vo.getGoodsIds();
        if(StringUtils.isNotEmpty(goodsIdsStr)){
            String[] split = goodsIdsStr.split("\n");
            for (String str : split){
                String trim = str.trim();
                goodsIdList.add(Integer.parseInt(trim));
            }
        }

        vo.setGoodsIdList(goodsIdList);
        buildCategoryId(vo);
        vo.setIsApp(vo.getIsApp());
//        List<NewActivityGoods> records = activityGoodsService.queryList(vo);
        IPage<NewActivityGoods> page = newActivityGoodsService.queryPage(vo);
        List<NewActivityGoods> records = page.getRecords();

        if (CollectionUtils.isEmpty(records)) {
            return result;
        }
        List<Long> goodsIds = records.stream().map(NewActivityGoods::getGoodsId).distinct().collect(Collectors.toList());
        List<Long> activityIds = records.stream().map(NewActivityGoods::getActivityId).distinct().collect(Collectors.toList()); //活动id
        List<NewActivity> activities = newActivityService.lambdaQuery().in(NewActivity::getId, activityIds).list();
        Map<Long, NewActivity> activityMap = activities.stream().collect(Collectors.toMap(NewActivity::getId, Function.identity(), (a, b) -> a));
        List<GoodsOutput> goodsOutputList = goodsClientFactory.queryAllActivityGoodsInfoByGoodsId(goodsIds);
        List<CostPriceDTO> costPriceDTOList = goodsClientFactory.getCostPriceDTOList(goodsIds, "1688");
        Map<Long, CostPriceDTO> costPriceDTOMap = org.apache.commons.collections4.CollectionUtils.emptyIfNull(costPriceDTOList).stream()
                .collect(Collectors.toMap(CostPriceDTO::getGoodsId, Function.identity()));
        Map<Long, GoodsOutput> goodsMap = goodsOutputList.stream().collect(Collectors.toMap(GoodsOutput::getId, Function.identity(), (a, b) -> a));

        List<ActivityGoodsResult> activityGoodsResults = new ArrayList<>();
        for (NewActivityGoods activityGoods : records) {
            Long goodsId = activityGoods.getGoodsId();
            GoodsOutput goods1 = goodsMap.get(goodsId);
            if (null == goods1) {
                continue;
            }

            ActivityGoodsResult activityGoodsResult = new ActivityGoodsResult();
            TransferUtils.transferBean(activityGoods, activityGoodsResult);
            activityGoodsResult.setId(activityGoods.getId());
            activityGoodsResult.setGoodsId(goodsId);
            activityGoodsResult.setGoodsName(activityGoods.getGoodsName());
            activityGoodsResult.setMainImage(goods1.getMainImage());
            activityGoodsResult.setFakeDiscount(GoodsDiscountLabelUtil.calculateDiscount(goods1.getMinPrice(), goods1.getMinMarketPrice()));

            activityGoodsResult.setShowPrice(activityGoods.getAfterDiscountMinPrice() + "~" + activityGoods.getAfterDiscountMaxPrice());
            activityGoodsResult.setAfterDiscountMinPrice(activityGoods.getAfterDiscountMinPrice());
            activityGoodsResult.setAfterDiscountMaxPrice(activityGoods.getAfterDiscountMaxPrice());
            activityGoodsResult.setAfterDiscountMinGrouponPrice(activityGoods.getAfterDiscountMinGrouponPrice());
            activityGoodsResult.setAfterDiscountMaxGrouponPrice(activityGoods.getAfterDiscountMaxGrouponPrice());
            NewActivity newActivity = activityMap.get(activityGoods.getActivityId());
            activityGoodsResult.setCategoryString(goods1.getCategoryString());
            //类目展示修改
            if (newActivity != null) {
                if (newActivity.getCatType() == 0) {
                    activityGoodsResult.setCategoryString("all");
                }
                if (newActivity.getCatType() == 2) {
                    activityGoodsResult.setCategoryString("除" + goods1.getCategoryString() + "之外");
                }
            }
            activityGoodsResult.setMinStock(goods1.getMinStock());
            activityGoodsResult.setSignUpTime(DateUtil.format(activityGoods.getCreateTime()));
            activityGoodsResult.setRealDiscount(activityGoods.getDiscount());
            activityGoodsResult.setStatus(activityGoods.getStatus());
            activityGoodsResult.setActivityId(activityGoods.getActivityId());
            activityGoodsResult.setActivityName(activityGoods.getActivityName());
            activityGoodsResult.setShopId(activityGoods.getShopId());
            activityGoodsResult.setShopName(activityGoods.getShopName());
            activityGoodsResult.setSort(activityGoods.getSort());
            activityGoodsResult.setFirstAuditTime(DateUtil.format(activityGoods.getFirstAuditTime()));
            activityGoodsResult.setAuditTime(DateUtil.format(activityGoods.getAuditTime()));
            activityGoodsResult.setGoodsFreightList(goods1.getGoodsFreightList());
            activityGoodsResult.setSkuIncreaseAfterDiscount(activityGoods.getSkuIncreaseAfterDiscount());

            CostPriceDTO costPriceDTO = costPriceDTOMap.get(goodsId);
            if(Objects.isNull(vo.getShopId()) && Objects.nonNull(costPriceDTO)){
                activityGoodsResult.setCostMinPrice(costPriceDTO.getMinCostPrice().multiply(BigDecimal.valueOf(2)));
                activityGoodsResult.setCostMaxPrice(costPriceDTO.getMaxCostPrice().multiply(BigDecimal.valueOf(2)));
            }
            activityGoodsResults.add(activityGoodsResult);
        }

        result.setRowCount(page.getTotal());
        result.setRecords(activityGoodsResults);
        return result;
    }

    @Override
    public PageView<ActivityGoodsResult> pageHistoryActivityGoodsRecords(ActivityGoodsVO vo){
        vo.setIsHistory(1);
        return this.pageActivityGoodsRecords(vo);
    }

    /**
     * 获取活动报名记录
     * @param vo
     * @return
     */
    @Override
    public PageView<ActivityGoodsResult> pageActivityGoodsRecords(ActivityGoodsVO vo) {

        vo.setShopId(getShopId());
        //设置时间6个月前
        LocalDateTime sixMonthsAgo = LocalDateTime.now().minusMonths(6);
        vo.setActivityEndTime(sixMonthsAgo);

        PageView<ActivityGoodsResult> result = new PageView<>(vo.getPageSize(), vo.getPageNow());

        if (Objects.nonNull(vo.getActivityId())) {
            if (CollectionUtils.isEmpty(vo.getActivityIds())) {
                vo.setActivityIds(Collections.singletonList(vo.getActivityId()));
            } else if (!vo.getActivityIds().contains(vo.getActivityId())) {
                vo.getActivityIds().add(vo.getActivityId());
            }
        }


        IPage<ActivityGoodsResult> page = newActivityGoodsService.pageHistoryActivityGoodsRecords(vo);
        if(CollectionUtils.isEmpty(page.getRecords())){
            return result;
        }
        List<Long> goodsIds = page.getRecords().stream().map(ActivityGoodsResult::getGoodsId).collect(Collectors.toList());

        List<GoodsOutput> goodsOutputList = goodsClientFactory.queryAllActivityGoodsInfoByGoodsId(goodsIds);
        List<CostPriceDTO> costPriceDTOList = goodsClientFactory.getCostPriceDTOList(goodsIds, "1688");
        Map<Long, CostPriceDTO> costPriceDTOMap = org.apache.commons.collections4.CollectionUtils.emptyIfNull(costPriceDTOList).stream()
                .collect(Collectors.toMap(CostPriceDTO::getGoodsId, Function.identity()));
        Map<Long, GoodsOutput> goodsMap = goodsOutputList.stream().collect(Collectors.toMap(GoodsOutput::getId, Function.identity(), (a, b) -> a));

        page.getRecords().forEach(activityGoods -> {

            activityGoods.setShowPrice(activityGoods.getAfterDiscountMinPrice() + "~" + activityGoods.getAfterDiscountMaxPrice());

            GoodsOutput goods1 = goodsMap.get(activityGoods.getGoodsId());
            if(Objects.nonNull(goods1)){
                activityGoods.setMainImage(goods1.getMainImage());
                activityGoods.setFakeDiscount(GoodsDiscountLabelUtil.calculateDiscount(goods1.getMinPrice(), goods1.getMinMarketPrice()));
                activityGoods.setCategoryString(goods1.getCategoryString());
                activityGoods.setMinStock(goods1.getMinStock());
                activityGoods.setGoodsFreightList(goods1.getGoodsFreightList());
            }
        });

        result.setRowCount(page.getTotal());
        result.setRecords(page.getRecords());
        return result;
    }

    private void buildCategoryId(ActivityGoodsVO vo) {
        if (null != vo.getSearchCategoryId() && vo.getSearchCategoryId() > 0) {
            List<Long> searchCategoryIds = new ArrayList<>();
            searchCategoryIds.add(vo.getSearchCategoryId());
            List<CategoryVO> categories = categoryClientFactory.queryChildCategoryByParentId(vo.getSearchCategoryId()).getData();
            if (CollectionUtils.isNotEmpty(categories)) {
                searchCategoryIds.addAll(categories.stream().map(CategoryVO::getId).collect(Collectors.toList()));
            }
            vo.setSearchCategoryIds(searchCategoryIds);
        }
    }


    @Override
    public PageView<ActivityGoodsResult> queryActivityGoodsPageForShop(ActivityGoodsVO vo) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String voghionToken = request.getHeader("clientInfo");
        // 查询redis
        String vohionTokenStr = redisApi.get(voghionToken);
        // 查看api权限
        JSONObject json = JSONObject.parseObject(vohionTokenStr);
        JSONObject user = JSONObject.parseObject(json.getJSONObject("user").toJSONString());
        Long shopId = user.getLong("shopId");
        String shopName = user.getString("shopName");

        vo.setShopId(shopId);
        vo.setShopName(shopName);
        return queryActivityGoodsResult(vo);
    }

    @Override
    public List<ActivityGoodsExport> exportActivityGoods(ActivityGoodsVO activityGoodsVO) {
        if (null == activityGoodsVO) {
            activityGoodsVO = new ActivityGoodsVO();
        }
        log.info("flashDeals导出参数：{}",JSONObject.toJSON(activityGoodsVO));
        List<ActivityGoodsExport> activityGoodsExports = new ArrayList<>();
        activityGoodsVO.setIsApp(false);
        //查询所有的在报名中的 活动id
        List<NewActivity> activities = queryActivityId(activityGoodsVO);
        if (CollectionUtils.isEmpty(activities)) {
            return activityGoodsExports;
        }
        //是否是商家的导入
        boolean merchant = activityGoodsVO.isMerchant();
        if(merchant){
            activityGoodsVO.setShopId(getShopId());
        }
        //处理类目信息
        buildCategoryId(activityGoodsVO);
        List<Long> shopIdList = new ArrayList<>();
        if(Objects.nonNull(activityGoodsVO.getShopId())){
            shopIdList.add(activityGoodsVO.getShopId());
        }
        if(org.apache.commons.lang3.StringUtils.isNotBlank(activityGoodsVO.getPrincipal())){
            List<FaMerchantsApplyDTO> faMerchantsApplyDTOS = goodsClientFactory.listFaMerchantsApplyListByPrinciple(activityGoodsVO.getPrincipal());
            log.info("ExportActivityGoods Principle = {}, faMerchantsApplyDTOS = {}", activityGoodsVO.getPrincipal(), faMerchantsApplyDTOS);
            if(CollectionUtils.isEmpty(faMerchantsApplyDTOS)){
                log.info("ExportActivityGoods PagefaMerchantsApplyDTOS is empty  Principle = {}, faMerchantsApplyDTOS = {}", activityGoodsVO.getPrincipal(), faMerchantsApplyDTOS);
                return activityGoodsExports;
            }
            List<Long> shopIds = faMerchantsApplyDTOS.stream().map(FaMerchantsApplyDTO::getShopId).collect(Collectors.toList());
            if(Objects.nonNull(activityGoodsVO.getShopId())){
                if(!shopIds.contains(activityGoodsVO.getShopId())){
                    log.info("ExportActivityGoods shopIds not contains shopId, Principle = {}, faMerchantsApplyDTOS = {}", activityGoodsVO.getPrincipal(), faMerchantsApplyDTOS);
                    return activityGoodsExports;
                }
            }else{
                shopIdList.addAll(shopIds);
            }
        }

        activityGoodsVO.setShopIdList(shopIdList);

        log.info("flashDeals导出参数 查询参数：{}",JSONObject.toJSON(activityGoodsVO));

        List<NewActivityGoods> activityGoods = new ArrayList<>();
        Integer pageNum = 1, pageSize = 500;
        do {
            activityGoodsVO.setPageNow(pageNum);
            activityGoodsVO.setPageSize(pageSize);
            List<NewActivityGoods> result = newActivityGoodsService.queryActivityBackendExportList(activityGoodsVO);
            if (CollectionUtils.isEmpty(result)) {
                log.info("flashDeals导出 result isEmpty pageNum : {} queryPar : {}", pageNum, JSONObject.toJSON(activityGoodsVO));
                break;
            }
            activityGoods = result;
            transferExportVo(activityGoods, activityGoodsExports, activities, activityGoodsVO);
            pageNum++;
        } while (true);


        return activityGoodsExports;

    }

    private void transferExportVo(List<NewActivityGoods> activityGoods, List<ActivityGoodsExport> activityGoodsExports, List<NewActivity> activities, ActivityGoodsVO activityGoodsVO) {

        if (CollectionUtils.isEmpty(activityGoods)) {
            return;
        }

        //处理是否改价的活动id
        List<Long> notModify = activities.stream().filter(activity -> activity.getStateType() != 1).map(NewActivity::getId).collect(Collectors.toList());
        Set<Long> goodsIdList = activityGoods.stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return;
        }

        List<List<Long>> partition = Lists.partition(Lists.newArrayList(goodsIdList), 200);
        List<CompletableFuture<List<GoodsOutput>>> futures = new ArrayList<>();
        for (List<Long> batch : partition) {
            CompletableFuture<List<GoodsOutput>> future = CompletableFuture.supplyAsync(() -> goodsClientFactory.queryGoodsOnlyByGoodsIds(new ArrayList<>(batch)).getData(), executor);
            futures.add(future);
        }

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        CompletableFuture<List<List<GoodsOutput>>> allResults = allFutures.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));

        List<List<GoodsOutput>> results = null;
        try {
            results = allResults.get();
        } catch (Exception e) {
            log.error("queryGoodsOnlyByGoodsIds completableFuture error activityId : {}", activityGoodsVO.getActivityId(), e);
        }

        if (CollectionUtils.isEmpty(results)) {
            return;
        }

        List<GoodsOutput> allGoodsOutputList = new ArrayList<>();
        for (List<GoodsOutput> goods : results) {
            allGoodsOutputList.addAll(goods);
        }

        Set<Long> cateIds = activityGoods.stream().map(NewActivityGoods::getCategoryId).collect(Collectors.toSet());

        List<CategoryVO> categories = categoryClientFactory.queryCategoryByIds(new ArrayList<>(cateIds)).getData();
        Map<Long, CategoryVO> cateMap = categories.stream().collect(Collectors.toMap(CategoryVO::getId, Function.identity(), (a, b) -> b));
        Map<Long, GoodsOutput> goodsMap = allGoodsOutputList.stream().collect(Collectors.toMap(GoodsOutput::getId, Function.identity(), (a, b) -> b));
        List<Long> resultShopIdList = activityGoods.stream().map(NewActivityGoods::getShopId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<com.voghion.marketing.model.po.FaMerchantsApply> faMerchantsApplyList = faMerchantsApplyService.lambdaQuery().in(com.voghion.marketing.model.po.FaMerchantsApply::getId, resultShopIdList).list();
        Map<Long, String> principalMap = org.apache.commons.collections4.CollectionUtils.emptyIfNull(faMerchantsApplyList).stream()
                .filter(e -> org.apache.commons.lang3.StringUtils.isNotBlank(e.getPrincipal()) && Objects.nonNull(e.getId()))
                .collect(Collectors.toMap(com.voghion.marketing.model.po.FaMerchantsApply::getId, com.voghion.marketing.model.po.FaMerchantsApply::getPrincipal));


        if (CollectionUtils.isNotEmpty(activityGoods)) {
            for (NewActivityGoods ag : activityGoods) {
                ActivityGoodsExport ac = new ActivityGoodsExport();
                GoodsOutput gds = goodsMap.get(ag.getGoodsId());
                CategoryVO cate = cateMap.get(ag.getCategoryId());
                if (gds != null) {
                    BigDecimal minPrice = gds.getMinPrice();
                    BigDecimal maxPrice = gds.getMaxPrice();
                    if (notModify.contains(ag.getActivityId())) {
                        minPrice = minPrice.multiply(ag.getDiscount()).setScale(2, BigDecimal.ROUND_UP);
                        maxPrice = maxPrice.multiply(ag.getDiscount()).setScale(2, BigDecimal.ROUND_UP);
                    }
                    ac.setGoodsCreateTime(gds.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    ac.setPrincipal(principalMap.get(gds.getShopId()));
                    ac.setIsShow(gds.getIsShow());
                    ac.setMinPrice(minPrice);
                    ac.setMaxPrice(maxPrice);
                    ac.setMinMarketPrice(gds.getMinMarketPrice() == null ? null : gds.getMinMarketPrice().multiply(ag.getDiscount()).setScale(2, BigDecimal.ROUND_UP));
                }

                ac.setCategoryName(cate == null ? "" : cate.getName());
                ac.setFirstLevelCategoryName(null == cate || null == cate.getFirstLevelCategory() ? "" : cate.getFirstLevelCategory().getName());
                ac.setDiscount(ag.getDiscount());
                ac.setCreateTime(DateUtil.format(ag.getCreateTime(), DateUtil.newFormat));
                ac.setSort(ag.getSort());
                ac.setStatusString(ag.getStatus() < 0 ? "待审核" : ag.getStatus() == 0 ? "落选" : "入选");
                ac.setFirstStatus(ag.getFirstStatus() < 0 ? "待审核" : ag.getFirstStatus() == 0 ? "落选" : "入选");
                ac.setGoodsName(ag.getGoodsName());
                ac.setGoodsId(ag.getGoodsId());
                ac.setShopName(ag.getShopName());
                ac.setAuditor(ag.getAuditor());
                ac.setAuditRemark(ag.getAuditRemark());
                ac.setAuditTime(DateUtil.format(ag.getAuditTime()));
                ac.setFirstAuditor(ag.getFirstAuditor());
                ac.setFirstAuditRemark(ag.getFirstAuditRemark());
                ac.setFirstAuditTime(DateUtil.format(ag.getFirstAuditTime()));

                activityGoodsExports.add(ac);

            }
        }
    }

    @Override
    public void asyncExportActivityGoods(ActivityGoodsVO vo) {
        log.info("asyncExport start {}", vo.getActivityId());
        NewActivity newActivity = newActivityService.selectById(vo.getActivityId());
        ExportTask exportTask = new ExportTask();
        Date date = new Date();
        exportTask.setStatus(0);
        exportTask.setBeginTimestamp(date.getTime());
        exportTask.setParams(newActivity.getId() + "_" + newActivity.getName());
        exportTask.setClassName("活动管理-商品导出_" + newActivity.getId() + "_" + newActivity.getName());
        exportTask.setCreator(clientInfoSupportService.getOperationUser().getId());
        exportTask.setCreateTime(date);
        exportTask.setGroup(1);
        exportTaskService.insert(exportTask);

        executor.execute(() -> {
            try {
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                ExcelWriter writer = new ExcelWriterBuilder().file(byteArrayOutputStream).head(ActivityGoodsExport.class).build();
                WriteSheet writeSheet = new WriteSheet();
                List<ActivityGoodsExport> activityGoodsExports = exportActivityGoods(vo);
                writer.write(activityGoodsExports, writeSheet);
                writer.finish();
                String url = bussCommonFactory.uploadAndReturnUrl(byteArrayOutputStream.toByteArray(), "export_flashdeal_product" + System.currentTimeMillis() + ".xlsx");
                log.info("asyncExportActivityGoods uploadExcel activityId {} exportSize {} url {}", vo.getActivityId(), activityGoodsExports.size(), url);
                ExportTask exportTaskUpdate = new ExportTask();
                Date now = new Date();
                exportTaskUpdate.setId(exportTask.getId());
                exportTaskUpdate.setEndTimestamp(now.getTime());
                exportTaskUpdate.setUpdateTime(now);
                exportTaskUpdate.setResult(url);
                exportTaskUpdate.setStatus(2);
                exportTaskService.updateById(exportTaskUpdate);

            } catch (Exception e) {
                log.error("asyncExportActivityGoods fail activityId {}  meg {}  stack {} ", vo.getActivityId(), e.getMessage(), e.getStackTrace());
                Date now = new Date();
                ExportTask exportTaskUpdate = new ExportTask();
                exportTaskUpdate.setId(exportTask.getId());
                exportTaskUpdate.setEndTimestamp(now.getTime());
                exportTaskUpdate.setUpdateTime(now);
                exportTaskUpdate.setStatus(3);
                exportTaskService.updateById(exportTaskUpdate);
            }
        });
    }

    @Override
    public List<ActivityGoodsShopExport> exportActivityGoodsForShop(ActivityGoodsVO activityGoodsVO) {
        if (null == activityGoodsVO) {
            activityGoodsVO = new ActivityGoodsVO();
        }
        log.info("flashDeals导出参数：{}",JSONObject.toJSON(activityGoodsVO));
        List<ActivityGoodsShopExport> activityGoodsExports = new ArrayList<>();
        activityGoodsVO.setIsApp(false);
        //查询所有的在报名中的 活动id
        List<NewActivity> activities = queryActivityId(activityGoodsVO);
        if (CollectionUtils.isEmpty(activities)) {
            return activityGoodsExports;
        }
        //是否是商家的导入
        boolean merchant = activityGoodsVO.isMerchant();
        if(merchant){
            activityGoodsVO.setShopId(getShopId());
        }
        //处理类目信息
        buildCategoryId(activityGoodsVO);
        log.info("flashDeals导出参数 查询参数：{}",JSONObject.toJSON(activityGoodsVO));
        List<NewActivityGoods> activityGoods = newActivityGoodsService.queryExportList(activityGoodsVO);
        if (CollectionUtils.isEmpty(activityGoods)) {
            return activityGoodsExports;
        }

        //处理是否改价的活动id
        List<Long> notModify = activities.stream().filter(activity -> activity.getStateType() != 1).map(NewActivity::getId).collect(Collectors.toList());
        Set<Long> goodsIdList = activityGoods.stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return activityGoodsExports;
        }
        Set<Long> cateIds = activityGoods.stream().map(NewActivityGoods::getCategoryId).collect(Collectors.toSet());
        //todo 减少远程调用 一次调用返回
        List<GoodsOutput> goods = goodsClientFactory.queryGoodsOnlyByGoodsIds(new ArrayList<>(goodsIdList)).getData();
        List<CategoryVO> categories = categoryClientFactory.queryCategoryByIds(new ArrayList<>(cateIds)).getData();
        Map<Long, CategoryVO> cateMap = categories.stream().collect(Collectors.toMap(CategoryVO::getId, Function.identity(), (a, b) -> b));
        Map<Long, GoodsOutput> goodsMap = goods.stream().collect(Collectors.toMap(GoodsOutput::getId, Function.identity(), (a, b) -> b));
        if (CollectionUtils.isNotEmpty(activityGoods)) {
            for (NewActivityGoods ag : activityGoods) {
                ActivityGoodsShopExport ac = new ActivityGoodsShopExport();
                GoodsOutput gds = goodsMap.get(ag.getGoodsId());
                CategoryVO cate = cateMap.get(ag.getCategoryId());
                if (gds != null) {
                    BigDecimal minPrice = gds.getMinPrice();
                    BigDecimal maxPrice = gds.getMaxPrice();
                    if (notModify.contains(ag.getActivityId())) {
                        minPrice = minPrice.multiply(ag.getDiscount()).setScale(2, BigDecimal.ROUND_UP);
                        maxPrice = maxPrice.multiply(ag.getDiscount()).setScale(2, BigDecimal.ROUND_UP);
                    }
                    ac.setMinPrice(minPrice);
                    ac.setMaxPrice(maxPrice);
                    ac.setMinMarketPrice(gds.getMinMarketPrice() == null ? null : gds.getMinMarketPrice().multiply(ag.getDiscount()).setScale(2, BigDecimal.ROUND_UP));
                }

                ac.setCategoryName(cate == null ? "" : cate.getName());
                ac.setFirstLevelCategoryName(null == cate || null == cate.getFirstLevelCategory() ? "" : cate.getFirstLevelCategory().getName());
                ac.setDiscount(ag.getDiscount());
                ac.setCreateTime(DateUtil.format(ag.getCreateTime(), DateUtil.newFormat));
                ac.setSort(ag.getSort());
                ac.setStatusString(ag.getStatus() < 0 ? "待审核" : ag.getStatus() == 0 ? "落选" : "入选");
                ac.setFirstStatus(ag.getFirstStatus() < 0 ? "待审核" : ag.getFirstStatus() == 0 ? "落选" : "入选");
                ac.setGoodsName(ag.getGoodsName());
                ac.setGoodsId(ag.getGoodsId());
                ac.setShopName(ag.getShopName());
                ac.setAuditRemark(ag.getAuditRemark());
                ac.setAuditTime(DateUtil.format(ag.getAuditTime()));
                ac.setFirstAuditRemark(ag.getFirstAuditRemark());
                ac.setFirstAuditTime(DateUtil.format(ag.getFirstAuditTime()));
                activityGoodsExports.add(ac);
            }
            return activityGoodsExports;
        }
        return null;

    }

    @Override
    public List<NewHomeActivityDataDTO> queryHomeActivityDataList(NewActivityGoodsDataDTO dto) {
        if (CollectionUtils.isEmpty(dto.getTypeList())) {
            return new ArrayList<>();
        }
        List<NewHomeActivityDataDTO> res = new ArrayList<>();
        for (Integer type : dto.getTypeList()) {
            NewActivityGoodsDataDTO query = new NewActivityGoodsDataDTO();
            query.setType(type);
            NewHomeActivityDataDTO newHomeActivityDataDTO = this.queryHomeActivityData(query);
            if (newHomeActivityDataDTO != null) {
                res.add(newHomeActivityDataDTO);
            }
        }
        return res;
    }

    @Override
    public void updateActivityGoodsMinPrice(ActivityGoodsGoodsUpdatePriceDto dto) {

    }


    private void initFirstCategory(List<CategoryVO> categories) {
        List<Long> firstCategoryIds = new ArrayList<>();
        for (CategoryVO category : categories) {
            if (1 == category.getLevel() || StringUtils.isBlank(category.getPids())) {
                firstCategoryIds.add(category.getId());
                continue;
            }
            String[] pathCategoryIds = category.getPids().split(",");
            if ("null".equals(pathCategoryIds[0])) {
                continue;
            }
            firstCategoryIds.add(Long.parseLong(pathCategoryIds[0]));
        }
        List<CategoryVO> firstCategories = categoryClientFactory.queryCategoryByIds(firstCategoryIds).getData();
        Map<Long, CategoryVO> firstLevelCategoryMap = firstCategories.stream().collect(Collectors.toMap(CategoryVO::getId, Function.identity(), (v1, v2) -> v2));
        for (CategoryVO category : categories) {
            if (1 == category.getLevel() || StringUtils.isBlank(category.getPids())) {
                category.setFirstLevelCategory(firstLevelCategoryMap.get(category.getId()));
                continue;
            }
            String[] pathCategoryIds = category.getPids().split(",");
            if (!"null".equals(pathCategoryIds[0])) {
                category.setFirstLevelCategory(firstLevelCategoryMap.get(Long.parseLong(pathCategoryIds[0])));
            }
        }
    }

    private List<NewGoodsOnlyDTO> getGoodsInfo(List<Long> goodsIdS) {
        List<NewGoodsOnlyDTO> goodsOnlyDTOS = new ArrayList<>();
        com.voghion.es.dto.RecommentGoodsListESModelDTO goodsListESModelDTO = new com.voghion.es.dto.RecommentGoodsListESModelDTO();
        goodsListESModelDTO.setGoodsId(goodsIdS);
        goodsListESModelDTO.setCountryName(getCountry());
        List<com.voghion.es.vo.GoodsESModelVo> goodsESModelVos = goodsEsService.queryGoodsByGoodsIdAndCountry(goodsListESModelDTO);
        if (CollectionUtils.isNotEmpty(goodsESModelVos)) {
            for (com.voghion.es.vo.GoodsESModelVo good : goodsESModelVos) {
                NewGoodsOnlyDTO goodsOnlyDTO = new NewGoodsOnlyDTO();
                TransferUtils.transferBean(good, goodsOnlyDTO);

                if (good.getCountryPrice() != null) {
                    goodsOnlyDTO.setPrice(good.getCountryPrice());
                }
                if (good.getCountryGrouponPrice() != null) {
                    goodsOnlyDTO.setGrouponPrice(good.getCountryGrouponPrice());
                }
                goodsOnlyDTO.setMarketPrice(good.getMinMarketPrice());
                // 计算折扣标签, 根据 最低售价/ 展示价格
                String discountLabel = GoodsDiscountLabelUtil.calculateDiscount(good.getMinPrice(), good.getMinMarketPrice());
                goodsOnlyDTO.setDiscountLabel(discountLabel);
                goodsOnlyDTO.setTag(good.getTag());
                GoodsExtConfigModel goodsExtConfigModel = good.getGoodsExtConfigModel();
                if (goodsExtConfigModel != null) {
                    goodsOnlyDTO.setTagIds(goodsExtConfigModel.getTagIds());
                }
                ListGoodsCommentModel goodsCommend = good.getListGoodsCommentModel();
                if (goodsCommend != null) {
                    goodsOnlyDTO.setScore(goodsCommend.getCommentAverage());
                    goodsOnlyDTO.setCommentNumber(goodsCommend.getCommentCount());
                }
                GoodsExtDetailModel goodsExtDetailModel = good.getGoodsExtDetailModel();
                if (goodsExtDetailModel != null) {
                    goodsOnlyDTO.setBrandId(goodsExtDetailModel.getBrandId());
                }
                goodsOnlyDTOS.add(goodsOnlyDTO);
            }
        }
        return goodsOnlyDTOS;
    }

    private String getCountry() {
        ClientInfoDTO clientInfoDTO = ThreadContext.getContext(SystemConstants.CLIENTINFO);
        if (null != clientInfoDTO && StringUtils.isNotEmpty(clientInfoDTO.getCountry())) {
            String country = clientInfoDTO.getCountry();
            String s = country.toUpperCase();
            log.info("请求头国家  参数：{}", s);
            return s;
        }
        return "";
    }

    /**
     * 查询初审的分页数据
     *
     * @param vo
     * @return
     */
    @Override
    public PageView<ActivityGoodsResult> queryFirstActivityGoodsPage(ActivityGoodsVO vo) {
        if (null == vo) {
            vo = new ActivityGoodsVO();
        }
        log.info("初审信息 查询初审列表分页数据 参数:{}", JSONObject.toJSONString(vo));
        PageView<ActivityGoodsResult> result = new PageView<>(vo.getPageSize(), vo.getPageNow());

        //查询所有的在报名中的 活动id
        List<NewActivity> newActivities = queryActivityId(vo);
        if (CollectionUtils.isEmpty(newActivities)) {
            return result;
        }
        //处理类目信息
        buildCategoryId(vo);
        vo.setIsApp(false);

        List<Long> shopIdList = new ArrayList<>();
        if(Objects.nonNull(vo.getShopId())){
            shopIdList.add(vo.getShopId());
        }
        if(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getPrincipal())){
            List<FaMerchantsApplyDTO> faMerchantsApplyDTOS = goodsClientFactory.listFaMerchantsApplyListByPrinciple(vo.getPrincipal());
            log.info("queryFirstActivityGoodsPage Principle = {}, faMerchantsApplyDTOS = {}", vo.getPrincipal(), faMerchantsApplyDTOS);
            if(CollectionUtils.isEmpty(faMerchantsApplyDTOS)){
                log.info("queryFirstActivityGoods PagefaMerchantsApplyDTOS is empty  Principle = {}, faMerchantsApplyDTOS = {}", vo.getPrincipal(), faMerchantsApplyDTOS);
                return result;
            }
            List<Long> shopIds = faMerchantsApplyDTOS.stream().map(FaMerchantsApplyDTO::getShopId).collect(Collectors.toList());
            if(Objects.nonNull(vo.getShopId())){
                if(!shopIds.contains(vo.getShopId())){
                    log.info("queryFirstActivityGoodsPage shopIds not contains shopId, Principle = {}, faMerchantsApplyDTOS = {}", vo.getPrincipal(), faMerchantsApplyDTOS);
                    return result;
                }
            }else{
                shopIdList.addAll(shopIds);
            }
        }
        vo.setShopIdList(shopIdList);
        log.info("初审信息 查询初审列表分页数据 最后请求参数:{}", JSONObject.toJSONString(vo));
        IPage<NewActivityGoods> page = newActivityGoodsService.queryFirstPage(vo);

        List<NewActivityGoods> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return result;
        }
        List<ActivityGoodsResult> activityGoodsResults = getActivityGoodsResults(records);
        result.setRowCount(page.getTotal());
        result.setRecords(activityGoodsResults);
        log.info("初审信息 查询初审列表成功");
        return result;
    }

    @Override
    public Boolean firstSelectOrLostActivityGoods(ActivityGoodsSelectVo vo) {
        CheckUtils.notNull(vo, MarketingResultCode.PARAMETER_ERROR);
        log.info("初审信息 运营入选/落选活动商品 vo:{}", JSON.toJSONString(vo));
        List<Long> ids = vo.getIds();
        if (CollectionUtils.isEmpty(ids) || (0 != vo.getSelect() && 1 != vo.getSelect())) {
            CheckUtils.check(true, MarketingResultCode.PARAMETER_ERROR);
        }
        BaseTokenUserInfo userInfo = getUserInfo();
        List<NewActivityGoods> activityGoodsList = new ArrayList<>();
        List<Long> needRemoveLockIds = new ArrayList<>();

        if(Objects.equals(vo.getSelect(), 0)){
            Collection<NewActivityGoods> newActivityGoods = newActivityGoodsService.listByIds(ids);
            List<Long> passGoodsIdList = newActivityGoods.stream().filter(e -> Objects.equals(e.getStatus(), 1)).map(NewActivityGoods::getGoodsId).collect(Collectors.toList());
            CheckUtils.check(CollectionUtils.isNotEmpty(passGoodsIdList), ActivityResultCode.fill(ActivityResultCode.PASS_NOT_LOST, StringUtils.join(passGoodsIdList, ",")));

            List<Long> activityIdList = newActivityGoods.stream().map(NewActivityGoods::getActivityId).distinct().collect(Collectors.toList());
            Collection<NewActivity> newActivities = newActivityService.listByIds(activityIdList);
            Date now = new Date();
            newActivities.forEach(newActivity -> {
                if(newActivity.getEffectStartTime().compareTo(now) <= 0
                        && newActivity.getEffectEndTime().compareTo(now) >= 0){
                    CheckUtils.check(true, ActivityResultCode.ACTIVITY_NOT_LOST);
                }
            });
        }

        if(Objects.equals(vo.getSelect(), 1)){
            checkPassDuplicate(vo.getIds());
        }
        for (Long id : ids) {
            Date date = new Date();
            NewActivityGoods activityGoods = new NewActivityGoods();
            activityGoods.setId(id);
            activityGoods.setFirstStatus(vo.getSelect());
            //如果初审落选 终审默认也落选
            if (0 == vo.getSelect()) {
                activityGoods.setStatus(vo.getSelect());
                activityGoods.setAuditTime(date);
            }
            activityGoods.setFirstAuditTime(date);
            if (null != userInfo) {
                activityGoods.setFirstAuditor(userInfo.getUserName());
            }
            activityGoods.setUpdateTime(date);
            if (0 == vo.getSelect()) {
                activityGoods.setFirstAuditRemark(vo.getRemark());
                needRemoveLockIds.add(id);
            }
            activityGoodsList.add(activityGoods);
        }
        if (CollectionUtils.isNotEmpty(needRemoveLockIds)) {
            List<Long> needRemoveLockGoodsIds = newActivityGoodsService.lambdaQuery()
                    .in(NewActivityGoods::getId, needRemoveLockIds)
                    .select(NewActivityGoods::getGoodsId)
                    .list()
                    .stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toList());
            goodsLockClientFactory.removeLock(needRemoveLockGoodsIds, Collections.singletonList(GoodsLockLabelTypEnums.FLASH_DEAL.getCode()));
        }
        boolean b = newActivityGoodsService.updateBatchById(activityGoodsList);
        CheckUtils.check(!b, MarketingResultCode.UPDATE_AUDIT_FAILED);
        log.info("初审信息 更新初审状态成功");
        return b;
    }

    /**
     * 获取活动商品
     *
     * @param records
     * @return
     */
    private List<ActivityGoodsResult> getActivityGoodsResults(List<NewActivityGoods> records) {
        List<Long> goodsIds = records.stream().map(NewActivityGoods::getGoodsId).distinct().collect(Collectors.toList());
        List<GoodsOutput> goodsOutputList = goodsClientFactory.queryAllActivityGoodsInfoByGoodsId(goodsIds);
        List<Long> shopIdS = records.stream().map(NewActivityGoods::getShopId).collect(Collectors.toList());
        List<FaMerchantsApply> faMerchantsApplies = goodsClientFactory.queryShopName(shopIdS);
        Map<Long, String> principalMap = org.apache.commons.collections4.CollectionUtils.emptyIfNull(faMerchantsApplies).stream()
                .filter(e -> StringUtils.isNotBlank(e.getPrincipal()))
                .collect(Collectors.toMap(FaMerchantsApply::getId, FaMerchantsApply::getPrincipal, (a, b) -> a));
        Map<Long, GoodsOutput> goodsMap = goodsOutputList.stream().collect(Collectors.toMap(GoodsOutput::getId, Function.identity(), (a, b) -> a));

        List<ActivityGoodsResult> activityGoodsResults = new ArrayList<>();
        List<CostPriceDTO> costPriceDTOList = goodsClientFactory.getCostPriceDTOList(goodsIds, "1688");
        Map<Long, CostPriceDTO> costPriceDTOMap = org.apache.commons.collections4.CollectionUtils.emptyIfNull(costPriceDTOList).stream()
                .collect(Collectors.toMap(CostPriceDTO::getGoodsId, Function.identity()));
        List<Long> activityIdList = records.stream().map(NewActivityGoods::getActivityId).collect(Collectors.toList());
        Collection<NewActivity> newActivities = newActivityService.listByIds(activityIdList);
        Map<Long, NewActivity> activityMap = newActivities.stream().collect(Collectors.toMap(NewActivity::getId, Function.identity()));
        for (NewActivityGoods activityGoods : records) {
            Long goodsId = activityGoods.getGoodsId();
            GoodsOutput goods1 = goodsMap.get(goodsId);
            if (null == goods1) {
                continue;
            }
            ActivityGoodsResult activityGoodsResult = new ActivityGoodsResult();
            TransferUtils.transferBean(activityGoods, activityGoodsResult);
            activityGoodsResult.setGoodsId(goodsId);
            activityGoodsResult.setMainImage(goods1.getMainImage());
            activityGoodsResult.setFakeDiscount(GoodsDiscountLabelUtil.calculateDiscount(goods1.getMinPrice(), goods1.getMinMarketPrice()));
            activityGoodsResult.setShowPrice(activityGoods.getAfterDiscountMinPrice() + "~" + activityGoods.getAfterDiscountMaxPrice());

            activityGoodsResult.setCategoryString(goods1.getCategoryString());
            activityGoodsResult.setMinStock(goods1.getMinStock());
            activityGoodsResult.setSignUpTime(DateUtil.format(activityGoods.getCreateTime()));
            activityGoodsResult.setRealDiscount(activityGoods.getDiscount());
            activityGoodsResult.setFirstAuditTime(DateUtil.format(activityGoods.getFirstAuditTime()));
            activityGoodsResult.setAuditTime(DateUtil.format(activityGoods.getAuditTime()));
            activityGoodsResult.setGoodsFreightList(goods1.getGoodsFreightList());
            activityGoodsResult.setPrincipal(principalMap.get(activityGoods.getShopId()));
            activityGoodsResult.setAutoSelectTime(activityGoods.getAutoSelectTime());
            NewActivity newActivity = activityMap.get(activityGoods.getActivityId());
            if(Objects.nonNull(newActivity)){
                activityGoodsResult.setEffectStartTime(DateUtil.format(newActivity.getEffectStartTime()));
                activityGoodsResult.setEffectEndTime(DateUtil.format(newActivity.getEffectEndTime()));
                //类目展示修改
                if (newActivity.getCatType() == 0) {
                    activityGoodsResult.setCategoryString("all");
                }
                if (newActivity.getCatType() == 2) {
                    activityGoodsResult.setCategoryString("除" + goods1.getCategoryString() + "之外");
                }
            }
            CostPriceDTO costPriceDTO = costPriceDTOMap.get(goodsId);
            if(Objects.nonNull(costPriceDTO)){
                activityGoodsResult.setCostMinPrice(costPriceDTO.getMinCostPrice().multiply(BigDecimal.valueOf(2)));
                activityGoodsResult.setCostMaxPrice(costPriceDTO.getMaxCostPrice().multiply(BigDecimal.valueOf(2)));
            }
            activityGoodsResults.add(activityGoodsResult);
        }
        return activityGoodsResults;
    }

    private List<NewActivity> queryActivityId(ActivityGoodsVO vo) {
        ActivityVO activityVO = new ActivityVO();
        activityVO.setSignUpStatus(vo.getSignUpStatus());
        activityVO.setId(vo.getActivityId());
        activityVO.setName(vo.getActivityName());
        activityVO.setType(vo.getType());
        List<NewActivity> activities = activityService.queryActivities(activityVO);
        if (CollectionUtils.isEmpty(activities)) {
            return null;
        }
        List<Long> acIds = activities.stream().map(NewActivity::getId).collect(Collectors.toList());
        log.info("queryActivityId|acIds:{}", acIds);
        vo.setActivityIds(acIds);
        return activities;
    }

    @Override
    public List<NewActivityGoods> queryCurrentApplyGoodsIds(Integer size, Long nowId) {
        NewActivity newActivity = newActivityService.queryCurrentApplyActivity();
        if (null == newActivity) {
            log.info("查询当前没有预报名的活动");
            return new ArrayList<>();
        }
        Long id = newActivity.getId();
        List<NewActivityGoods> newActivityGoods = newActivityGoodsService.queryCurrentGoodsByActivityId(id, size, nowId);
        if (CollectionUtils.isEmpty(newActivityGoods)) {
            log.info("此活动id：" + id + " 查询不到活动商品 返回");
            return new ArrayList<>();
        }
        return newActivityGoods;

    }

    @Override
    public void updateBatchById(List<ActivityGoodsDTO> list) {
        newActivityGoodsService.updateBatchById(TransferUtils.transferList(list, NewActivityGoods.class));
    }


    /**
     * 更新排序字段
     * @param vo
     * @return
     */
    @Override
    public Boolean updateActivityGoods(ActivityGoodsVO vo) {
        CheckUtils.notNull(vo, ActivityResultCode.PARAMS_NULL_ERROR);
        CheckUtils.notNull(vo.getId(), ActivityResultCode.PARAMS_NULL_ERROR);
        CheckUtils.notNull(vo.getSort(), ActivityResultCode.PARAMS_NULL_ERROR);

        NewActivityGoods newActivityGoods = new NewActivityGoods();
        newActivityGoods.setId(vo.getId());
        newActivityGoods.setSort(vo.getSort());
        return newActivityGoodsService.updateById(newActivityGoods);
    }


    @Override
    public Boolean bindVirGoodsItemsId(BindVirGoodsIdVO bindVirGoodsIdVO) {
        log.info("bindVirGoodsItemsId {}", bindVirGoodsIdVO);
        NewActivity newActivity = newActivityService.selectById(bindVirGoodsIdVO.getId());
        CheckUtils.check(Objects.isNull(newActivity), CustomResultCode.fill(MarketingResultCode.ACTIVITY_ID_NOT_EXIST, String.valueOf(bindVirGoodsIdVO.getId())));
        Long originVirGoodsItemsId = newActivity.getVirGoodsItemsId();
        newActivity.setVirGoodsItemsId(bindVirGoodsIdVO.getVirGoodsItemsId());
        newActivity.setUpdateTime(new Date());
        newActivityService.updateById(newActivity);
        log.info("bindVirGoodsIds origin virGoodsItemsId = {}, new virGoodsItemsId = {}", originVirGoodsItemsId, bindVirGoodsIdVO.getVirGoodsItemsId());
        return Boolean.TRUE;
    }

    List<GoodsESVo> listGoods(List<Long> goodsIdList, String[] includes) {
        try {
            SearchResponse response = restHighLevelClient.search(
                    new SearchRequest("goods_es_new_v2")
                            .source(new SearchSourceBuilder()
                                    .fetchSource(includes, null)
                                    .size(goodsIdList.size())
                                    .query(QueryBuilders.termsQuery("_id", goodsIdList))

                            ),
                    RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            log.info("listGoods size {}  {}", goodsIdList.size(), response.getHits().getTotalHits());
            if (hits != null && hits.length > 0) {
                return Arrays.stream(hits).map((e) -> {
                    String jsonStr = e.getSourceAsString();
                    return JSON.parseObject(jsonStr, GoodsESVo.class);
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.info("listGoods fail {} {} {}", goodsIdList, e.getMessage(), e.getStackTrace());
            return Lists.newArrayList();
        }
        return Lists.newArrayList();
    }
    void sync2CustomListItems(List<NewActivityGoods> goodsList, Long activityId, Long virGoodsId){
        if(CollectionUtils.isEmpty(goodsList)) {
            return;
        }

        List<Long> goodIds = goodsList.stream().map(NewActivityGoods::getGoodsId).distinct().collect(Collectors.toList());
        Map<Long, NewActivityGoods> activityGoodsMap = goodsList.stream().collect(Collectors.toMap(NewActivityGoods::getGoodsId, Function.identity(), (a, b) -> a));
        List<GoodsESVo> goodsESVos = listGoods(goodIds, includes);
        log.info("sync2CustomListItems call queryGoodsByGoodsIds, goodIds size = {}, goodsESVos size = {}", goodIds.size(), goodsESVos.size());

        LocalDateTime now = LocalDateTime.now();
//        List<CustomGoodsItems> customGoodsItemsList = new ArrayList<>();
        List<Long> negativeList = Arrays.asList(40L, 45L, 50L);
        List<CustomListItems> collect = goodsESVos.stream()
                .filter(g -> {
                    if(Objects.nonNull(g)) return true;
                    log.info("sync2CustomListItems exist null item, activityId = {}, goodIds = {}.", activityId, g);
                    return false;
                })
                .map(goods -> {
                    NewActivityGoods newActivityGoods = activityGoodsMap.get(goods.getId());
                    CustomListItems customListItems = new CustomListItems();
                    customListItems.setCustomId(virGoodsId);
                    customListItems.setGoodsId(goods.getId());
                    int showArea = Objects.nonNull(newActivityGoods) && Objects.nonNull(newActivityGoods.getSort()) ? newActivityGoods.getSort() : 9999;
                    customListItems.setShowArea(showArea);
                    String isShow = goods.getIsShow() == null || goods.getIsShow().length() > 1 ? "0" : goods.getIsShow();
                    customListItems.setIsShow(isShow);
                    customListItems.setCreateTime(now);
                    customListItems.setCountry(goods.getCountry());
                    customListItems.setAppChannel(1);
                    customListItems.setCategoryId(goods.getCategoryId());
                    customListItems.setPrice(goods.getMinPrice());
                    customListItems.setOrginalSales(0L);
                    customListItems.setAutoSelectStatus(newActivityGoods.getAutoSelectStatus());
                    customListItems.setAutoSelectFailReason(newActivityGoods.getAutoSelectFailReason());
                    customListItems.setAutoSelectTime(newActivityGoods.getAutoSelectTime());

                    GoodsExtConfigModel goodsExtConfigModel = goods.getGoodsExtConfigModel();
                    Integer isNegative;
                    if(Objects.nonNull(goodsExtConfigModel) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(goodsExtConfigModel.getTagIds())){
                        List<Long> tagIds = goodsExtConfigModel.getTagIds();
                        isNegative = CollectionUtils.containsAny(tagIds, negativeList) ? 1 : 0;
                    }else {
                        isNegative = 0;
                    }
                    customListItems.setIsNegative(isNegative);
                    return customListItems;
                }).collect(Collectors.toList());

        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(collect)){
            customListItemsService.insertBatch(collect);

            // 记录同步明细
            recordSyncOrRemove(collect, 1, activityId, virGoodsId);
            log.info("sync2CustomListItems, activity id = {}, virGoodsId = {}, sync list = {}", activityId, virGoodsId, collect);
        }

        // 如果条数不正确，则发送钉钉
        if(collect.size() != goodIds.size()){
            try {
                Set<Long> goodsSet = collect.stream().map(CustomListItems::getGoodsId).collect(Collectors.toSet());
                List<Long> notExistList = goodIds.stream().filter(g -> !goodsSet.contains(g)).collect(Collectors.toList());
                dingDingCoreService.callActivityGoodsSync(new StringBuffer(100)
                        .append("活动id为：").append(activityId).append("\n")
                        .append("告警信息为：")
                        .append("报名的商品和es查询出的商品条数不一致，有如下在es查询不出：")
                        .append(org.apache.commons.lang3.StringUtils.join(notExistList, ","))
                        .toString()
                );
            }catch (Exception e) {
                log.error("sync2CustomListItems call ding ding fail e = {}", e.getMessage(), e);
            }
        }
    }

    private CustomGoodsItems getCustomGoodsItems(GoodsESVo goods, Long customId, String country) {
        CustomGoodsItems customGoodsItems = new CustomGoodsItems();
        customGoodsItems.setGoodsId(goods.getId());
        customGoodsItems.setCustomId(customId);
        customGoodsItems.setCreateTime(LocalDateTime.now());
        customGoodsItems.setCategoryId(goods.getCategoryId());
        customGoodsItems.setPrice(goods.getMinPrice());
        customGoodsItems.setCountry(country);
        return customGoodsItems;
    }

    @Override
    public Boolean removeVirGoodsItems(SyncVirGoodsIdVO syncVirGoodsIdVO) {
        CheckUtils.check(Objects.isNull(syncVirGoodsIdVO) || Objects.isNull(syncVirGoodsIdVO.getId()), BusinessMarketingResultCode.PARAMS_NULL_ERROR);
        long startTime = System.currentTimeMillis();

        // 校验 虚拟列表id是否存在
        NewActivity newActivity = newActivityService.queryActivityById(syncVirGoodsIdVO.getId());
        CheckUtils.check(Objects.isNull(newActivity), CustomResultCode.fill(MarketingResultCode.ACTIVITY_ID_NOT_EXIST, String.valueOf(syncVirGoodsIdVO.getId())));
        CheckUtils.check(Objects.isNull(newActivity.getVirGoodsItemsId()) || newActivity.getVirGoodsItemsId() < 1,
                MarketingResultCode.BIND_VIR_GOODS_ID_BEFORE);

        CustomList customList = customListService.getById(newActivity.getVirGoodsItemsId());
        CheckUtils.check(Objects.isNull(customList), MarketingResultCode.VIR_GOODS_ID_INFO_NOT_EXIST);
        Boolean tryLock = redisTemplate.opsForValue().setIfAbsent(getActivityVirItemsRemoveLockKey(syncVirGoodsIdVO.getId()), "1", 1, TimeUnit.MINUTES);
        CheckUtils.check(Objects.equals(Boolean.FALSE, tryLock), MarketingResultCode.NOT_FREQUENTLY_SYNC);

        Long customId = newActivity.getVirGoodsItemsId();
        Long activityId = newActivity.getId();
        CustomListItems startItem = customListItemsService.lambdaQuery().eq(CustomListItems::getCustomId, customId)
                .orderByAsc(CustomListItems::getId).last("limit 1").one();
        if(Objects.isNull(startItem)){
            log.info("removeVirGoodsItems startItem is null {}", activityId);
            return true;
        }
        CustomListItems endItem = customListItemsService.lambdaQuery().eq(CustomListItems::getCustomId, customId)
                .orderByDesc(CustomListItems::getId).last("limit 1").one();
        removeCustomListItems(activityId, customId, startItem.getId(), endItem.getId());

        redisTemplate.delete(getActivityVirItemsSyncLockKey(syncVirGoodsIdVO.getId()));
        log.info("removeVirGoodsItems, activity id = {}, cost = {} ms", syncVirGoodsIdVO.getId(), System.currentTimeMillis() - startTime);
        return Boolean.TRUE;
    }
    void removeCustomListItems(Long activityId, Long customId, long start, long end){
        log.info("removeCustomListItems {} {} {} {} ", activityId, customId, start, end);
        int size = 1000;
        for(long i = start; i <= end; i += size){
            long finalI = i;
            activityPool.execute(() ->{
                List<CustomListItems> customListItemsList = customListItemsService.lambdaQuery()
                        .select(CustomListItems::getId, CustomListItems::getGoodsId)
                        .ge(CustomListItems::getId, finalI)
                        .lt(CustomListItems::getId, finalI + size)
                        .eq(CustomListItems::getCustomId, customId)
                        .list();
                if(CollectionUtils.isNotEmpty(customListItemsList)){
                    recordSyncOrRemove(customListItemsList, 0, activityId, customId);
                    List<Long> ids = customListItemsList.stream().map(CustomListItems::getId).collect(Collectors.toList());
                    customListItemsService.removeByIds(ids);
                }
            });
        }
    }

    @Override
    public Boolean syncVirGoodsItems(SyncVirGoodsIdVO syncVirGoodsIdVO) {
        CheckUtils.check(Objects.isNull(syncVirGoodsIdVO) || Objects.isNull(syncVirGoodsIdVO.getId()), BusinessMarketingResultCode.PARAMS_NULL_ERROR);
        long start = System.currentTimeMillis();

        // 校验 活动和虚拟id是否绑定
        NewActivity newActivity = newActivityService.queryActivityById(syncVirGoodsIdVO.getId());
        CheckUtils.check(Objects.isNull(newActivity) || Objects.equals(newActivity.getIsDel(), 1), CustomResultCode.fill(MarketingResultCode.ACTIVITY_ID_NOT_EXIST, String.valueOf(syncVirGoodsIdVO.getId())));
        CheckUtils.check(Objects.isNull(newActivity.getVirGoodsItemsId()) || newActivity.getVirGoodsItemsId() < 1,
                MarketingResultCode.BIND_VIR_GOODS_ID_BEFORE);

        Date now = new Date();
        CheckUtils.check(syncVirGoodsIdVO.isBackend()
                        && (newActivity.getEffectStartTime().compareTo(now) > 0 || newActivity.getEffectEndTime().compareTo(now) < 0) ,
                MarketingResultCode.ACTIVITY_NOT_EFFECT);

        CustomList customList = customListService.getById(newActivity.getVirGoodsItemsId());
        CheckUtils.check(Objects.isNull(customList), MarketingResultCode.VIR_GOODS_ID_INFO_NOT_EXIST);

        Boolean tryLock = redisTemplate.opsForValue().setIfAbsent(getActivityVirItemsSyncLockKey(syncVirGoodsIdVO.getId()), "1", 1, TimeUnit.MINUTES);
        CheckUtils.check(Objects.equals(Boolean.FALSE, tryLock), MarketingResultCode.NOT_FREQUENTLY_SYNC);

        // 活动时间可能被更改
//        checkDup(newActivity.getId());

        // 先对虚拟商铺列表id进行删除，原有活动绑定的虚拟商铺列表id可能被更改
        removeVirGoodsItems(syncVirGoodsIdVO);
        syncGoods(newActivity.getId(), newActivity.getVirGoodsItemsId());

        log.info("syncVirGoodsItems, activity id = {}, cost = {} ms", syncVirGoodsIdVO.getId(), System.currentTimeMillis() - start);
        redisTemplate.delete(getActivityVirItemsSyncLockKey(syncVirGoodsIdVO.getId()));

        //同步的时候打标签
        activityOriginalPriceCoreService.backupPrice(syncVirGoodsIdVO.getId());
        return Boolean.TRUE;
    }
    public void checkDup(Long activityId){
        Long id = 0L;
        List<NewActivityGoods> list;
        Set<ActivityOriginalPrice> goodsIdSet = new HashSet<>();
        do{
            list = newActivityGoodsService.lambdaQuery()
                    .select(NewActivityGoods::getId, NewActivityGoods::getGoodsId)
                    .eq(NewActivityGoods::getActivityId, activityId)
                    .eq(NewActivityGoods::getStatus, 1)
                    .gt(NewActivityGoods::getId, id)
                    .orderByAsc(NewActivityGoods::getId)
                    .last("limit 50")
                    .list();
            if(CollectionUtils.isEmpty(list)){
                break;
            }
            id = list.get(list.size() - 1).getId();
            List<Long> goodsIdList = list.stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toList());
            List<ActivityOriginalPrice> activityOriginalPriceList = activityOriginalPriceService.lambdaQuery()
                    .in(ActivityOriginalPrice::getGoodsId, goodsIdList)
                    .eq(ActivityOriginalPrice::getIsDel, 0)
                    .list();
            if(CollectionUtils.isNotEmpty(activityOriginalPriceList)){
                goodsIdSet.addAll(activityOriginalPriceList);
            }
        }while (true);

        log.info("checkDup {} {}", activityId, JSON.toJSONString(goodsIdSet));
        if(CollectionUtils.isNotEmpty(goodsIdSet)){
            int retry = 0;
            while(CollectionUtils.isNotEmpty(goodsIdSet) && retry < 3){
                try {
                    TimeUnit.SECONDS.sleep(RandomUtils.nextInt(10, 30));
                }catch (Exception ignored){}
                retry++;
                int size = goodsIdSet.size();
                List<ActivityOriginalPrice> activityOriginalPriceList = activityOriginalPriceService.lambdaQuery()
                        .eq(ActivityOriginalPrice::getActivityId, activityId)
                        .in(ActivityOriginalPrice::getGoodsId, goodsIdSet)
                        .eq(ActivityOriginalPrice::getIsDel, 0)
                        .list();
                goodsIdSet.clear();
                goodsIdSet.addAll(activityOriginalPriceList);
                log.info("checkDup retry {} {} {} {}", activityId, retry, size, goodsIdSet.size());
            }
            if(CollectionUtils.isNotEmpty(goodsIdSet)){
                log.info("checkDup after filter {} {}", activityId, JSON.toJSONString(goodsIdSet));
                List<Long> collect = goodsIdSet.stream().map(ActivityOriginalPrice::getActivityId).collect(Collectors.toList());
                List<NewActivity> activityList = newActivityService.lambdaQuery().in(NewActivity::getId, collect).list();
                Map<Long, String> nameMap = activityList.stream().collect(Collectors.toMap(NewActivity::getId, NewActivity::getName));
                for (ActivityOriginalPrice activityOriginalPrice : goodsIdSet) {
                    newActivityGoodsService.lambdaUpdate()
                            .set(NewActivityGoods::getStatus, 0)
                            .set(NewActivityGoods::getAuditRemark, "商品同时参加了活动_" + nameMap.get(activityOriginalPrice.getActivityId()))
                            .eq(NewActivityGoods::getGoodsId, activityOriginalPrice.getGoodsId())
                            .eq(NewActivityGoods::getActivityId, activityId)
                            .update();
                }
            }
        }
    }
    void syncGoods(Long activityId, Long customId){
        Long id = 0L;
        List<NewActivityGoods> goodsList;
        do {
            goodsList = newActivityGoodsService.list(new QueryWrapper<NewActivityGoods>()
                    .select("id", "goods_id", "sort")
                    .gt("id", id)
                    .eq("activity_id", activityId)
                    .eq("status", 1)
                    .eq("is_del", 0)
                    .orderByAsc("id")
                    .last("limit 500")
            );
            log.info("syncGoods syncVirGoodsIds goodsList, id ={}, size = {}", id, goodsList.size());
            if (CollectionUtils.isEmpty(goodsList)) {
                break;
            }
            id = goodsList.get(goodsList.size() - 1).getId();
            List<NewActivityGoods> finalGoodsList = goodsList;
            activityPool.execute(() ->{
                sync2CustomListItems(finalGoodsList, activityId, customId);
            });
        } while (CollectionUtils.isNotEmpty(goodsList));
    }
    private String getActivityVirItemsSyncLockKey(Long id){
        return VIR_ITEMS_SYNC_LOCK + id;
    }
    private String getActivityVirItemsRemoveLockKey(Long id){
        return VIR_ITEMS_REMOVE_LOCK + id;
    }

    @Override
    public Boolean autoSyncAllActivityVirGoodsItems(List<NewActivity> newActivities) {
        log.info("autoSyncAllActivityVirGoodsItems newActivities = {}", newActivities);
        newActivities.forEach(activity -> {
            try {
                Boolean tryLock = redisTemplate.opsForValue().setIfAbsent(VIR_ITEMS_AUTO_SYNC_LOCK + activity.getId(), "1", 1, TimeUnit.HOURS);
                if(Boolean.FALSE.equals(tryLock)) {
                    log.info("autoSyncAllActivityVirGoodsItems try lock fail activity id = {}", activity.getId());
                    return;
                }
                syncVirGoodsItems(new SyncVirGoodsIdVO().setId(activity.getId()));
            }catch (Exception e) {
                log.error("autoSyncAllActivityVirGoodsItems fail, activity id = {}, message = {}, e = {}", activity.getId(), e.getMessage(), e);
                try {
                    dingDingCoreService.callActivityGoodsSync(new StringBuffer()
                            .append("活动id为：")
                            .append(activity.getId())
                            .append("\n")
                            .append("异常信息为：")
                            .append(e.getMessage())
                            .append(Arrays.toString(e.getStackTrace()))
                            .toString());
                } catch (Exception e1) {
                    log.error("autoSyncAllActivityVirGoodsItems call ding ding fail e = {}", e1.getMessage(), e1);
                }
            }
        });
        return Boolean.TRUE;
    }

    @Override
    public Boolean autoRemoveAllActivityVirGoodsItems(List<NewActivity> newActivities) {
        log.info("autoRemoveAllActivityVirGoodsItems newActivities = {}", newActivities);
        newActivities.forEach(activity -> {
            try {
                Boolean tryLock = redisTemplate.opsForValue().setIfAbsent(VIR_ITEMS_AUTO_SYNC_LOCK + activity.getId(), "1", 1, TimeUnit.HOURS);
                if(Boolean.FALSE.equals(tryLock)) {
                    log.info("autoRemoveAllActivityVirGoodsItems try lock fail activity id = {}", activity.getId());
                    return;
                }
                removeVirGoodsItems(new SyncVirGoodsIdVO().setId(activity.getId()));
            }catch (Exception e) {
                log.error("autoRemoveAllActivityVirGoodsItems fail, activity id = {}, message = {}, e = {}", activity.getId(), e.getMessage(), e);
            }
        });
        return Boolean.TRUE;
    }

    @Override
    public List<ActivityGoodsBriefDTO> listActivityBriefInfoByGoodsId(List<Long> goodsIds) {

        // 查询还未结束的活动
        List<NewActivity> newActivities = newActivityService.lambdaQuery()
                .gt(NewActivity::getEffectEndTime, LocalDateTime.now())
                .in(NewActivity::getType, Arrays.asList(GoodsActivityTypeEnum.FLASH_DEAL.getCode(), GoodsActivityTypeEnum.SELECTION.getCode()))
                .list();
        if(CollectionUtils.isEmpty(newActivities)) {
            log.info("listActivityBriefInfoByGoodsId newActivities is empty, goodsIds = {}", goodsIds);
            return new ArrayList<>();
        }
        List<Long> activityIds = newActivities.stream().map(NewActivity::getId).collect(Collectors.toList());
        List<NewActivityGoods> newActivityGoodsList = newActivityGoodsService.lambdaQuery()
                .in(NewActivityGoods::getActivityId, activityIds)
                .in(NewActivityGoods::getGoodsId, goodsIds)
                .list();
        if(CollectionUtils.isEmpty(newActivityGoodsList)){
            log.info("listActivityBriefInfoByGoodsId newActivityGoodsList is empty, goodsIds = {}", goodsIds);
            return new ArrayList<>();
        }
        Map<Long, NewActivity> activityMap = newActivities.stream().collect(Collectors.toMap(NewActivity::getId, Function.identity(), (a, b) -> a));
        List<ActivityGoodsBriefDTO> activityGoodsBriefDTOList = newActivityGoodsList.stream()
                .map(goods -> {
                    ActivityGoodsBriefDTO activityGoodsBriefDTO = new ActivityGoodsBriefDTO();
                    NewActivity newActivity = activityMap.get(goods.getActivityId());
                    activityGoodsBriefDTO.setGoodsId(goods.getGoodsId());
                    activityGoodsBriefDTO.setActivityTypeStatus(newActivity.getType());
                    activityGoodsBriefDTO.setActivityTypeDes(GoodsActivityTypeEnum.getDescByCode(newActivity.getType()));
                    activityGoodsBriefDTO.setActivityName(newActivity.getName());
                    activityGoodsBriefDTO.setEffectTimeRange(com.voghion.marketing.util.DateUtil.formatDate(newActivity.getEffectStartTime(), com.voghion.marketing.util.DateUtil.FORMAT1) +
                            "~" + com.voghion.marketing.util.DateUtil.formatDate(newActivity.getEffectEndTime(), com.voghion.marketing.util.DateUtil.FORMAT1));
                    activityGoodsBriefDTO.setSignTime(com.voghion.marketing.util.DateUtil.formatDate(goods.getCreateTime(), com.voghion.marketing.util.DateUtil.FORMAT1));
                    activityGoodsBriefDTO.setFirstApprovalStatus(goods.getFirstStatus());
                    activityGoodsBriefDTO.setFirstApprovalRemarks(goods.getFirstAuditRemark());
                    activityGoodsBriefDTO.setLastApprovalStatus(goods.getStatus());
                    activityGoodsBriefDTO.setLastApprovalRemarks(goods.getAuditRemark());
                    activityGoodsBriefDTO.setDiscount(newActivity.getLowestDiscount());
                    activityGoodsBriefDTO.setActivityId(newActivity.getId());
                    activityGoodsBriefDTO.setEffectStartTime(newActivity.getEffectStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());

                    return activityGoodsBriefDTO;
                }).collect(Collectors.toList());
        log.info("NewActivityGoodsCoreService listActivityBriefInfoByGoodsId activityGoodsBriefDTOList = {}", activityGoodsBriefDTOList);
        return activityGoodsBriefDTOList;
    }

    @Override
    public void batchSignUpAndPassFirstProcess(ActivityBatchSignUpDTO dto) {

        log.info("batchSignUpAndPassFirstProcess dto = {}.", dto);
        CheckUtils.check(Objects.isNull(dto) || Objects.isNull(dto.getActivityId()) || Objects.isNull(dto.getActivityTypeStatus())
                || CollectionUtils.isEmpty(dto.getGoodsIds()), BusinessMarketingResultCode.PARAMS_NULL_ERROR);
        CheckUtils.notNull(dto.getActivityId(), BusinessMarketingResultCode.PARAMS_NULL_ERROR);
        NewActivity activity = activityService.selectById(dto.getActivityId());
        CheckUtils.notNull(activity, MarketingResultCode.ACTIVITY_NOT_EXIST);
        List<Long> goodsIds = dto.getGoodsIds();
        List<GoodsESVo> goodsList = goodsEsService.queryGoodsByGoodsIds(goodsIds);

        // check
        newActivityGoodsSignUpCheck(goodsIds, activity, goodsList);

        Map<Long, GoodsESVo> goodsMap = goodsList.stream().collect(Collectors.toMap(GoodsESVo::getId, Function.identity(), (a, b) -> b));
        List<Long> existGoods = newActivityGoodsService.queryExistGoodsIds(dto.getActivityId(), goodsIds);

        //查询进行中flashDeal活动的商品原价格
        Map<Long, List<ActivityOriginalPriceDto>> startingGoodsPriceMap;
        List<NewActivity> startingActivitys = activityService.queryCurrentNormalActivity();
        if (CollectionUtils.isNotEmpty(startingActivitys)) {
            List<ActivityOriginalPriceDto> startingGoodsPriceInfo = Lists.newArrayList();
            for (NewActivity newActivity : startingActivitys) {
                startingGoodsPriceInfo.addAll(goodsClientFactory.queryStartingActivityGoodsInfo(newActivity.getId(), goodsIds));

            }
            startingGoodsPriceMap = startingGoodsPriceInfo.stream().collect(Collectors.groupingBy(ActivityOriginalPriceDto::getGoodsId));
        } else {
            startingGoodsPriceMap = Maps.newHashMap();
        }

        String userName = getUserName();

        List<NewActivityGoods> saveList = new ArrayList<>();
        Date date = new Date();
        for (Long goodsId : goodsIds) {
            GoodsESVo goods = goodsMap.get(goodsId);
            if (!existGoods.contains(goodsId)) {
                NewActivityGoods ag = new NewActivityGoods();
                ag.setActivityId(dto.getActivityId());
                ag.setActivityName(activity.getName());
                ag.setGoodsId(goodsId);
                ag.setGoodsName(goods.getName());
                ag.setCategoryId(goods.getCategoryId());
                ag.setShopId(goods.getShopId());
                ag.setMainImage(goods.getMainImage());
                ag.setShopName(goods.getShopName());
                ag.setDiscount(BigDecimal.ONE);

                List<ActivityOriginalPriceDto> activityOriginalPriceDtoList = startingGoodsPriceMap.get(goodsId);
                if (CollectionUtils.isNotEmpty(activityOriginalPriceDtoList)) {
                    activityOriginalPriceDtoList.stream().map(ActivityOriginalPriceDto::getOldPrice).min(BigDecimal::compareTo).ifPresent(ag::setMinPrice);
                    activityOriginalPriceDtoList.stream().map(ActivityOriginalPriceDto::getOldPrice).max(BigDecimal::compareTo).ifPresent(ag::setMaxPrice);
                    activityOriginalPriceDtoList.stream().map(ActivityOriginalPriceDto::getOldPrice).min(BigDecimal::compareTo).ifPresent(ag::setAfterDiscountMinPrice);
                    activityOriginalPriceDtoList.stream().map(ActivityOriginalPriceDto::getOldPrice).max(BigDecimal::compareTo).ifPresent(ag::setAfterDiscountMaxPrice);

                    List<BigDecimal> oldGroupPriceList = activityOriginalPriceDtoList.stream().map(ActivityOriginalPriceDto::getOldGrouponPrice).filter(Objects::nonNull).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(oldGroupPriceList)) {
                        oldGroupPriceList.stream().min(BigDecimal::compareTo).ifPresent(ag::setAfterDiscountMinGrouponPrice);
                        oldGroupPriceList.stream().max(BigDecimal::compareTo).ifPresent(ag::setAfterDiscountMaxGrouponPrice);
                    }
                } else {
                    ag.setMinPrice(goods.getMinPrice());
                    ag.setMaxPrice(goods.getMaxPrice());
                    ag.setAfterDiscountMinPrice(goods.getMinPrice());
                    ag.setAfterDiscountMaxPrice(goods.getMaxPrice());
                    ag.setAfterDiscountMinGrouponPrice(goods.getMinGrouponPrice());
                    ag.setAfterDiscountMaxGrouponPrice(goods.getMaxGrouponPrice());
                }

                ag.setOperator(userName);
                ag.setCreateTime(date);
                ag.setUpdateTime(date);
                ag.setStatus(-1);
                ag.setFirstStatus(1);
                ag.setFirstAuditTime(date);
                ag.setAuditTime(date);
                ag.setFirstAuditor(userName);
                ag.setAuditor(userName);

                // 小二添加的不需要改价
                ag.setNeedModifyPrice(0);

                //小二添加确认是商品渠道报名
                ag.setApplyChannel(0);

                saveList.add(ag);
//                redisApi.sSet("LockGoods", goods.getId());

            }
        }

        if (CollectionUtils.isNotEmpty(saveList)) {
            //补全小二信息
            completePrincipalInfo(saveList);

            newActivityGoodsService.insertBatch(saveList);

            newActivityService.updateNewActivitySignUpNums(dto.getActivityId(), (long) saveList.size(),DigitalOperationsEnum.ADD);
            //lock goods
//            goodsClientFactory.updateLockBatchById(needLockList);
            if (activity.getType() == 1) {
                goodsLockClientFactory.addLock(goodsIds, Collections.singletonList(GoodsLockLabelTypEnums.FLASH_DEAL.getCode()));
            }
        }
    }

    @Override
    public void autoPassActivityGoods() {
        List<NewActivity> newActivities = newActivityService.listAllSignUp();

        if(CollectionUtils.isNotEmpty(newActivities)){
            newActivities.forEach(newActivity ->{
                autoPassActivityGoods(newActivity.getId());
            });
        }

        List<ActivityFullReduction> activityFullReductions = activityFullReductionService.listAllSignUp();
        if(CollectionUtils.isNotEmpty(activityFullReductions)){
            activityFullReductions.forEach(newActivity ->{
                autoPassFullActivityGoods(newActivity.getId());
            });
        }

    }

    @Override
    public PageView<GoodsDTO> pageSignUpGoods(ActivityShopLimitVO vo) {
        CheckUtils.check(Objects.isNull(vo) || Objects.isNull(vo.getActivityId()), MarketingResultCode.PARAMETER_ERROR);
        long pageSize = vo.getPageSize();
        long pageNow = vo.getPageNow();
        Long activityId = vo.getActivityId();
        NewActivity newActivity = newActivityService.getById(activityId);
        Integer goodsRunDays = newActivity.getGoodsRunDays();
        Integer sales = newActivity.getSales();
        PageView<GoodsDTO> pageView = new PageView<>((int) pageNow, (int) pageSize);
        if(Objects.isNull(goodsRunDays) || Objects.isNull(sales)) return pageView;
        Long shopId = getShopId();
        List<OutDbGoodsEveryDayVO> outDbGoodsEveryDayList = getOutDbGoodsEveryDayList(shopId, goodsRunDays, sales, 1, 999);
        if(CollectionUtils.isEmpty(outDbGoodsEveryDayList)) return pageView;
        List<Long> goodsIdList = outDbGoodsEveryDayList.stream().map(OutDbGoodsEveryDayVO::getGoodsId).collect(Collectors.toList());
        List<GoodsESVo> goodsESVoList =  goodsEsService.queryGoodsByGoodsIds(goodsIdList);
        Map<Long, GoodsESVo> goodsMap = goodsESVoList.stream().collect(Collectors.toMap(GoodsESVo::getId, Function.identity()));

        // 查询已经参与的活动商品
        List<NewActivity> newActivities = newActivityService.listAllSignupStart2EffectEnd();
        List<Long> activityIdList = newActivities.stream().map(NewActivity::getId).collect(Collectors.toList());
        List<NewActivityGoods> newActivityGoodsList = newActivityGoodsService.lambdaQuery()
                .in(NewActivityGoods::getActivityId, activityIdList)
                .in(NewActivityGoods::getGoodsId, goodsIdList).list();
        Map<Long, NewActivityGoods> newActivityGoodsMap = newActivityGoodsList.stream().collect(Collectors.toMap(NewActivityGoods::getGoodsId, Function.identity(), (a, b) -> a));
        Set<Long> categoryIdSet = goodsESVoList.stream().map(GoodsESVo::getCategoryId).collect(Collectors.toSet());
        Map<Long, String> categoryPathMap = categoryClientFactory.getCategoryPathByIds(new ArrayList<>(categoryIdSet)).getData();
        List<GoodsDTO> collect = outDbGoodsEveryDayList.stream()
                .filter(goodsEveryDayVO -> {
                    GoodsESVo goodsESVo = goodsMap.get(goodsEveryDayVO.getGoodsId());
                    if(Objects.isNull(goodsESVo)) return false;
                    if(!Objects.equals(goodsESVo.getIsShow(), "1")) return false;
                    return Objects.isNull(newActivityGoodsMap.get(goodsEveryDayVO.getGoodsId()));
                })
                .map(goodsEveryDayVO -> {
                    GoodsDTO goodsDTO = new GoodsDTO();
                    GoodsESVo goodsESVo = goodsMap.get(goodsEveryDayVO.getGoodsId());
                    BeanCopyUtil.copyProperties(goodsESVo, goodsDTO);
                    goodsDTO.setSales(goodsEveryDayVO.getDealCnt());
                    goodsDTO.setKeyName(goodsRunDays + "日订单数");
                    goodsDTO.setCategoryPath(categoryPathMap.get(goodsESVo.getCategoryId()));
                    return goodsDTO;
                }).collect(Collectors.toList());
        collect.sort(Comparator.comparing(GoodsDTO::getSales).reversed());
        int size = collect.size();
        long start = (pageNow - 1) * pageSize;
        long end = Math.min(pageNow * pageSize, size);
        if(start >= collect.size()) {
            return pageView;
        }
        List<GoodsDTO> subList = collect.subList((int)start, (int)end);
        pageView.setRecords(subList);
        pageView.setPageCount((size + pageSize - 1) / pageSize);
        pageView.setRowCount(size);
        return pageView;
    }

    @Override
    public PageView<Long> pageGoods(GoodsCostDTO goodsCostDTO) {
        IPage<NewActivityGoods> page = newActivityGoodsService.lambdaQuery()
                .select(NewActivityGoods::getGoodsId)
                .eq(NewActivityGoods::getStatus, 1)
                .in(NewActivityGoods::getActivityId, goodsCostDTO.getActivityId())
                .eq(NewActivityGoods::getIsDel, 0)
                .lt(NewActivityGoods::getDiscount, goodsCostDTO.getMaxDiscount())
                .le(NewActivityGoods::getDiscount, goodsCostDTO.getMaxDiscount())
                .orderByDesc(NewActivityGoods::getId)
                .page(new Page<>(goodsCostDTO.getPageNow(), goodsCostDTO.getPageSize()));
        PageView<Long> pageView = new PageView<>();
        pageView.setRowCount(page.getTotal());
        pageView.setRecords(page.getRecords().stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toList()));
        return pageView;
    }

    @Override
    public void asyncExport(Long id) {
        log.info("asyncExport start {}", id);
        NewActivity newActivity = newActivityService.selectById(id);
        ExportTask exportTask = new ExportTask();
        Date date = new Date();
        exportTask.setStatus(0);
        exportTask.setBeginTimestamp(date.getTime());
        exportTask.setParams(newActivity.getId() + "_" + newActivity.getName());
        exportTask.setClassName("活动报名商品导出_" + newActivity.getId() + "_" + newActivity.getName());
        exportTask.setCreator(clientInfoSupportService.getOperationUser().getId());
        exportTask.setCreateTime(date);
        exportTask.setGroup(1);
        exportTaskService.insert(exportTask);

        executor.execute(() ->{
            try {
                String url = uploadExcel(id);
                log.info("asyncExport uploadExcel {} {}", id, url);
                ExportTask exportTaskUpdate = new ExportTask();
                Date now = new Date();
                exportTaskUpdate.setId(exportTask.getId());
                exportTaskUpdate.setEndTimestamp(now.getTime());
                exportTaskUpdate.setUpdateTime(now);
                exportTaskUpdate.setResult(url);
                exportTaskUpdate.setStatus(2);
                exportTaskService.updateById(exportTaskUpdate);

            } catch (Exception e) {
                log.error("asyncExport fail {} {} {} ", id, e.getMessage(), e.getStackTrace());
                Date now = new Date();
                ExportTask exportTaskUpdate = new ExportTask();
                exportTaskUpdate.setId(exportTask.getId());
                exportTaskUpdate.setEndTimestamp(now.getTime());
                exportTaskUpdate.setUpdateTime(now);
                exportTaskUpdate.setStatus(3);
                exportTaskService.updateById(exportTaskUpdate);
            }
        });

    }

    @Override
    public void removeEffectActivityGoods(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        Collection<NewActivityGoods> newActivityGoods = newActivityGoodsService.selectByIds(idList);
        if (CollectionUtils.isEmpty(newActivityGoods)) {
            log.info("loseGoods newActivityGoods is empty {}", idList);
            return;
        }
        List<Long> activityIdList = newActivityGoods.stream().map(NewActivityGoods::getActivityId).collect(Collectors.toList());
        Collection<NewActivity> newActivities = newActivityService.selectByIds(activityIdList);
        Map<Long, NewActivity> activityMap = newActivities.stream().collect(Collectors.toMap(NewActivity::getId, Function.identity()));
        Map<Long, List<NewActivityGoods>> activityGoodsMap = newActivityGoods.stream().collect(Collectors.groupingBy(NewActivityGoods::getActivityId));

        activityGoodsMap.forEach((key, value) -> {
            NewActivity newActivity = activityMap.get(key);
            if (newActivity.getEffectStartTime().compareTo(new Date()) > 0 || newActivity.getEffectEndTime().compareTo(new Date()) < 0) {
                log.info("loseGoods activity effect time not contain {}", newActivity);
                return;
            }

            // 1、移除列表
            Long virGoodsItemsId = newActivity.getVirGoodsItemsId();
            if(Objects.nonNull(virGoodsItemsId)){
                log.info("loseGoods virGoodsItemsId is not null {}", newActivity);
                List<Long> goodsIdList = value.stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toList());
                deleteCustomItems(virGoodsItemsId, goodsIdList, key);
            }

            // 2、移除标签
            // 3、恢复价格
            sendBatchMessage(value, activityMap.get(key), 2);
        });
    }
    /**
     * 批量发送消息
     *
     */
    private void sendBatchMessage(List<NewActivityGoods> activityGoodsList, NewActivity newActivity, Integer type) {
        activityGoodsList.forEach(activityGoods -> {
            ActivityTagGoodsDTO activityTagGoodsDTO = new ActivityTagGoodsDTO();
            activityTagGoodsDTO.setGoodsId(activityGoods.getGoodsId());
            activityTagGoodsDTO.setDiscount(activityGoods.getDiscount());
            activityTagGoodsDTO.setShowDiscountMin(newActivity.getShowDiscountMin());
            activityTagGoodsDTO.setShowDiscountMax(newActivity.getShowDiscountMax());
            activityTagGoodsDTO.setActivityId(activityGoods.getActivityId());
            activityTagGoodsDTO.setTagId(newActivity.getLabelTagId());
            activityTagGoodsDTO.setEffectStartTime(DateTimeUtil.date2LocalDateTime(newActivity.getEffectStartTime()));
            activityTagGoodsDTO.setEffectEndTime(DateTimeUtil.date2LocalDateTime(newActivity.getEffectEndTime()));
            activityTagGoodsDTO.setType(type);
            SendResult sendResult = mqSender.sendOrderly(ACTIVITY_GOODS_PRICE_TAG, activityTagGoodsDTO, activityTagGoodsDTO.getGoodsId());
            SendStatus sendStatus = sendResult.getSendStatus();
            if (sendStatus != SendStatus.SEND_OK) {
                log.info("sendBatchMessage fail {} {}", sendResult, activityTagGoodsDTO);
            }
        });
    }

    @Override
    public List<ActivityGoodsSimpleResult> queryCurrentActivityByGoodsId(List<Long> goodsIdList) {
        //查询当前正在进行中的活动集合
        List<NewActivity> newActivities = activityService.queryCurrentNormalActivity();
        if(CollectionUtils.isEmpty(newActivities)){
            return new ArrayList<>();
        }
        List<Long> activityIdList = newActivities.stream().map(NewActivity::getId).collect(Collectors.toList());
        List<NewActivityGoods> newActivityGoods = newActivityGoodsService.queryActivityGoods(activityIdList, goodsIdList);
        if(CollectionUtils.isEmpty(newActivityGoods)){
            return new ArrayList<>();
        }
        return newActivityGoods.stream().map(item -> {
            ActivityGoodsSimpleResult result = new ActivityGoodsSimpleResult();
            result.setGoodsId(item.getGoodsId());
            result.setActivityName(item.getActivityName());
            result.setActivityId(item.getActivityId());
            return result;
        }).collect(Collectors.toList());
    }

    @Override
    public void refreshNewActivityGoods() {
        List<NewActivity> list = newActivityService.lambdaQuery().gt(NewActivity::getEffectEndTime, new Date())
                .eq(NewActivity::getIsDel, 0).list();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> activityIdList = list.stream().map(NewActivity::getId).collect(Collectors.toList());
        long pageNum = 1, pageSize = 50;
        RateLimiter limiter = RateLimiter.create(1);
        do {
            boolean tryAcquire = limiter.tryAcquire(200, TimeUnit.MILLISECONDS);
            if(!tryAcquire) {
                log.info("refreshNewActivityGoods tryAcquire limit {}", pageNum);
                continue;
            }
            IPage<NewActivityGoods> page = newActivityGoodsService.lambdaQuery()
                    .in(NewActivityGoods::getActivityId, activityIdList)
                    .eq(NewActivityGoods::getIsDel, 0)
                    .orderByAsc(NewActivityGoods::getId)
                    .page(new Page<>(pageNum++, pageSize));
            List<NewActivityGoods> records = page.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                log.info("refreshNewActivityGoods end {}", pageNum);
                break;
            }
            List<Long> goodsIdList = records.stream().map(NewActivityGoods::getGoodsId).distinct().collect(Collectors.toList());
            List<GoodsESVo> goodsESVos = goodsEsService.queryGoodsByGoodsIds(goodsIdList);
            Map<Long, GoodsESVo> goodsESVoMap = goodsESVos.stream().filter(Objects::nonNull).collect(Collectors.toMap(GoodsESVo::getId, Function.identity()));
            List<NewActivityGoods> collect = records.stream()
                    .map(e -> {
                        GoodsESVo goodsESVo = goodsESVoMap.get(e.getGoodsId());
                        NewActivityGoods newActivityGoods = new NewActivityGoods();
                        if (Objects.nonNull(goodsESVo)) {
                            newActivityGoods.setIsShow(goodsESVo.getIsShow());
                        } else {
                            newActivityGoods.setIsShow("0");
                        }
                        newActivityGoods.setId(e.getId());
                        return newActivityGoods;
                    }).collect(Collectors.toList());
            newActivityGoodsService.updateBatchById(collect);

        } while (true);
    }

    @Override
    public List<String> listFailReason(String failReason) {
        List<NewActivityGoods> newActivityGoods = newActivityGoodsService.getBaseMapper().selectList(new QueryWrapper<NewActivityGoods>()
                .select("distinct auto_select_fail_reason")
                .like(StringUtils.isNotBlank(failReason), "auto_select_fail_reason", failReason)
                .isNotNull("auto_select_fail_reason")
        );

        return newActivityGoods.stream().filter(item -> !item.getAutoSelectFailReason().contains("参考均价")).map(NewActivityGoods::getAutoSelectFailReason).collect(Collectors.toList());
    }


    void sendBatchMessage(List<NewActivityGoods> activityGoodsList, Map<Long, NewActivity> activityMap, String topic) {
        List<Message<ActivityTagGoodsDTO>> messageList = new ArrayList<>();
        activityGoodsList.forEach(activityGoods -> {
            NewActivity newActivity = activityMap.get(activityGoods.getActivityId());
            ActivityTagGoodsDTO activityTagGoodsDTO = new ActivityTagGoodsDTO();
            activityTagGoodsDTO.setGoodsId(activityGoods.getGoodsId());
            activityTagGoodsDTO.setDiscount(activityGoods.getDiscount());
            activityTagGoodsDTO.setActivityId(activityGoods.getActivityId());
            activityTagGoodsDTO.setTagId(newActivity.getLabelTagId());
            activityTagGoodsDTO.setEffectStartTime(DateTimeUtil.date2LocalDateTime(newActivity.getEffectStartTime()));
            activityTagGoodsDTO.setEffectEndTime(DateTimeUtil.date2LocalDateTime(newActivity.getEffectEndTime()));
            messageList.add(MessageBuilder.withPayload(activityTagGoodsDTO).build());
        });

        if (CollectionUtils.isNotEmpty(messageList)) {
            List<List<Message<ActivityTagGoodsDTO>>> messageSplitList = ArrayUtils.splitArray(messageList, 500);
            for (List<Message<ActivityTagGoodsDTO>> messages : messageSplitList) {
                mqSender.sendBatch(topic, messages);

                //发送消息
                log.info("开始发送消息, 消息大小为, size = {}, messageList = {}", messages.size(), JSON.toJSONString(messages));
            }
        }
    }

    @Override
    public void deleteCustomItems(Long customId, List<Long> goodsIdList, Long activityId){
        List<CustomListItems> customListItemsList = customListItemsService.lambdaQuery().eq(CustomListItems::getCustomId, customId).in(CustomListItems::getGoodsId, goodsIdList).list();
        if(CollectionUtils.isEmpty(customListItemsList)){
            log.info("deleteCustomItems customListItemsList is empty {} {} {}", customId, goodsIdList, activityId);
            return;
        }
        List<Long> customIdList = customListItemsList.stream().map(CustomListItems::getId).collect(Collectors.toList());
        customListItemsService.deleteByIds(customIdList);

//        Map<Long, List<Long>> collect = customListItemsList.stream().collect(Collectors.groupingBy(CustomListItems::getCustomId, Collectors.mapping(CustomListItems::getGoodsId, Collectors.toList())));
//        collect.forEach((k, v) -> customGoodsItemsService.deleteByCustomIdAndGoodsIds(k, v));
        recordSyncOrRemove(customListItemsList, 0, activityId, customId);
    }

    @Override
    public void signByFile(MultipartFile file) {
        // 必须在请求线程内完成原文件的保存。
        byte[] uploadBytes = null;
        try {
            // 备份源文件
            uploadBytes = file.getBytes();
            CheckUtils.notNull(uploadBytes, MarketingResultCode.IMPORT_FAILED);
        }catch (Exception e){
            log.error("signByFile 读取文件出错:{}, {}", file.getOriginalFilename(), e);
            CheckUtils.check(true, MarketingResultCode.IMPORT_FAILED);
        }

        // 异步上传
        UserDTO operationUser = clientInfoSupportService.getOperationUser();
        LocalDateTime now = LocalDateTime.now();
        final byte[] finalUploadBytes = uploadBytes;
        final AtomicInteger total = new AtomicInteger(0);
        final String taskName = "活动商品文件批量报名_" + file.getOriginalFilename();
        final Integer[] taskId = {null};
        final Long[] activityId = {null};
        log.info("signByFile, file:{}, operator:{}.", file.getOriginalFilename(), operationUser.getName());
        executor.execute(() -> {
            try {
                // 上传文件。防止网络问题导致备份失败。但是不影响执行
                String originalFileUrl = uploadOriginalFile(file.getOriginalFilename(), finalUploadBytes);

                // 记录status = 1 处理中
                taskId[0] = recordUploadTask(null, taskName, operationUser, 1, null, null, originalFileUrl, null);

                // 读取和处理i上传文件。失败结果保存在failList中。
                List<ActivityGoodsSignUpFailVO> failList = new ArrayList<>();
                boolean processToEnd = analysisExcelData(finalUploadBytes, total, activityId, failList, operationUser);

                // 失败文件上传
                String url = uploadFailListAsExcel(failList);

                callDing(CollectionUtils.isEmpty(failList) ? 0 : failList.size(), file.getOriginalFilename(), now, activityId[0], total, operationUser, processToEnd);

                boolean failed = !processToEnd || (total.get() == failList.size());
                recordUploadTask(taskId[0], taskName, operationUser, failed ? 3 : 2,
                        total.get() - failList.size(), failList.size(), originalFileUrl, url);

            } catch (Exception e) {
                log.error("signByFile error, file:{}, operator:{}.", file.getOriginalFilename(), operationUser.getName(), e);
                recordUploadTask(taskId[0], taskName, operationUser, 3, null, null, null, null);
            }
        });
    }

    private String uploadOriginalFile(String fileName, byte[] finalUploadBytes) {
        String originalFileUrl = null;
        try {
            originalFileUrl = bussCommonFactory.uploadAndReturnUrl(finalUploadBytes, "原文件_" + DateFormatUtils.format(new Date(), "yyyy-MM-dd_HH-mm-ss") + "_" + fileName);
            log.info("signByFile 上传备份文件: {}", originalFileUrl);
        } catch (Exception e){
            log.error("signByFile 上传备份文件出错:{}, {}", fileName, e);
        }
        return originalFileUrl;
    }

    private String uploadFailListAsExcel(List<ActivityGoodsSignUpFailVO> failList) {
        String url = null;
        if (CollectionUtils.isNotEmpty(failList)) {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            ExcelWriter writer = new ExcelWriterBuilder()
                    .file(byteArrayOutputStream)
                    .head(ActivityGoodsSignUpFailVO.class).build();
            WriteSheet writeSheet = new WriteSheet();
            writer.write(failList, writeSheet);
            writer.finish();
            try {
                url = bussCommonFactory.uploadAndReturnUrl(byteArrayOutputStream.toByteArray(), "sign_up_goods_" + System.currentTimeMillis() + ".xlsx");
            } catch (Exception e) {
                log.error("signByFile 上传结果文件出错，内容cnt：{}", failList.size(), e);
            }
        }
        return url;
    }

    private boolean analysisExcelData(byte[] finalUploadBytes, AtomicInteger total, Long[] activityId, List<ActivityGoodsSignUpFailVO> failList,  UserDTO operationUser) {
        boolean processedToEnd = true;
        ByteArrayInputStream bais= new ByteArrayInputStream(finalUploadBytes);
        try {
            EasyExcel.read(bais, ActivityGoodsSignUpVO.class, new AnalysisEventListener<ActivityGoodsSignUpVO>() {
                @Override
                public void invoke(ActivityGoodsSignUpVO activityGoodsVO, AnalysisContext analysisContext) {

                    total.incrementAndGet();//计数
                    String msg = "";
                    if (Objects.isNull(activityGoodsVO) || Objects.isNull(activityGoodsVO.getActivityId())
                            || Objects.isNull(activityGoodsVO.getGoodsId())
                            || Objects.isNull(activityGoodsVO.getDiscount())
                    ) {
                        msg = "参数为空";
                    } else {
                        try {
                            ActivityGoodsSignUpVo vo = new ActivityGoodsSignUpVo();
                            vo.setActivityId(activityGoodsVO.getActivityId());
                            vo.setGoodsIds(activityGoodsVO.getGoodsId().toString());
                            vo.setDiscount(activityGoodsVO.getDiscount());
                            vo.setUserName(operationUser.getName());
                            activityId[0] = activityGoodsVO.getActivityId();
                            ActivityGoodsSignUpResultVO upResultVO = signUpByOperator(vo, true);
                            log.info("signByFile success activityId: {} , goodsId: {}, upResultVO: {}", activityGoodsVO.getActivityId(), activityGoodsVO.getGoodsId(), upResultVO);
                        } catch (CustomException customException) {
                            msg = customException.getBusinessResultCode().getMsg();
                            log.info("signByFile customException fail goods:{}  msg: {} ", activityGoodsVO.getGoodsId(), msg);
                        } catch (Exception e) {
                            msg = "系统异常";
                            log.info("signByFile fail goods: {} msg: {} emsg:{}", activityGoodsVO.getGoodsId(), msg, e.getMessage(), e);
                        }
                    }

                    if (StringUtils.isNotBlank(msg)) {
                        ActivityGoodsSignUpFailVO activityGoodsSignUpFailVO = new ActivityGoodsSignUpFailVO();
                        activityGoodsSignUpFailVO.setActivityId(String.valueOf(activityGoodsVO.getActivityId()));
                        activityGoodsSignUpFailVO.setGoodsId(String.valueOf(activityGoodsVO.getGoodsId()));
                        activityGoodsSignUpFailVO.setDiscount(String.valueOf(activityGoodsVO.getDiscount()));
                        activityGoodsSignUpFailVO.setReason(msg);
                        failList.add(activityGoodsSignUpFailVO);

                    }
                }

                @Override
                public void onException(Exception exception, AnalysisContext context) throws Exception{
                    if (exception instanceof ExcelDataConvertException) {
                        total.incrementAndGet();//计数
                        ExcelDataConvertException cvtException = (ExcelDataConvertException)exception;
                        log.error("第{}行，第{}列解析异常, 原始:{}", cvtException.getRowIndex(), cvtException.getColumnIndex(), context.readRowHolder().getCellMap());
                        try {
                            Map<Integer, String> cellMap = context.readRowHolder().getCellMap().entrySet().stream()
                                    .collect(Collectors.toMap(Map.Entry::getKey, e -> Optional.ofNullable(e.getValue()).map(Objects::toString).orElse("")));
                            ActivityGoodsSignUpFailVO activityGoodsSignUpFailVO = new ActivityGoodsSignUpFailVO();
                            activityGoodsSignUpFailVO.setActivityId(cellMap.get(0));
                            activityGoodsSignUpFailVO.setGoodsId(cellMap.get(1));
                            activityGoodsSignUpFailVO.setDiscount(cellMap.get(2));
                            activityGoodsSignUpFailVO.setReason(MessageFormat.format("第{0}列字段格式异常", cvtException.getColumnIndex()));
                            failList.add(activityGoodsSignUpFailVO);
                        } catch (Exception e) {
                            log.error("signByFile 错误日志记录失败：", e);
                        }
                    }else {
                        throw exception;
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {

                }
            }).sheet().headRowNumber(1).doRead();
        } catch (Exception e) {
            processedToEnd = false;
            log.error("signByFile 解析处理文件异常。", e);
        }
        return processedToEnd;
    }

    private Integer recordUploadTask(Integer updateId, String taskName, UserDTO operationUser, Integer statusCode, Integer successCnt, Integer errorCnt, String originalFileUrl, String outputFileUrl) {
        BatchProcess exportTask = new BatchProcess();
        exportTask.setStatus(statusCode);
        exportTask.setClassName(taskName);
        exportTask.setCreator(operationUser.getId());
        exportTask.setCreateTime(LocalDateTime.now());
        exportTask.setGroup(1);
        exportTask.setSuccessCnt(successCnt);
        exportTask.setSuccessOutputFile(originalFileUrl);
        exportTask.setErrorCnt(errorCnt);
        exportTask.setErrorOutputFile(outputFileUrl);
        exportTask.setInputFile(originalFileUrl);

        if (updateId == null) {
            batchProcessService.insert(exportTask);
            log.info("BatchProcess inserted:{}, id", exportTask.getId());
            return exportTask.getId();
        }else {
            exportTask.setId(updateId);
            log.info("BatchProcess updated:{}, id", exportTask.getId());
            batchProcessService.updateById(exportTask);
            return updateId;
        }
    }

    @Override
    public PageView<GoodsSignUpVO> pageGoodsSignUpVO(GoodsSignUpQueryVO queryVO) {
        List<Long> goodIdList = null;
        if(org.apache.commons.lang3.StringUtils.isNotBlank(queryVO.getGoodsIdStr())){
            goodIdList = Arrays.stream(queryVO.getGoodsIdStr().split("\n")).map(Long::parseLong).collect(Collectors.toList());
        }
        CheckUtils.check(Objects.nonNull(goodIdList) && goodIdList.size() > 1000, MarketingResultCode.MAX_GOODS_SIZE_1000);

        // 0 - 待开始 1 - 进行中 2 - 已结束
        List<Integer> statusList = queryVO.getStatusList();

        Date now = new Date();
        List<NewActivity> activityList = newActivityService.lambdaQuery()
                .like(StringUtils.isNotBlank(queryVO.getActivityName()), NewActivity::getName, queryVO.getActivityName())
                .eq(Objects.nonNull(queryVO.getActivityId()), NewActivity::getId, queryVO.getActivityId())
                .eq(queryVO.getType() != null, NewActivity::getType, queryVO.getType())
                .like(StringUtils.isNotBlank(queryVO.getActivityName()), NewActivity::getName, queryVO.getActivityName())
                .eq(NewActivity::getIsDel, 0)
                .and(CollectionUtils.isNotEmpty(statusList), (swp) -> {
                    swp.or(Objects.nonNull(statusList) && statusList.contains(0), x -> x.gt(NewActivity::getEffectStartTime, now));
                    swp.or(Objects.nonNull(statusList) && statusList.contains(1), x -> x.le(NewActivity::getEffectStartTime, now)
                            .gt(NewActivity::getEffectEndTime, now));
                    Date date = DateUtils.addDays(now, -90);
                    swp.or(Objects.nonNull(statusList) && statusList.contains(2), x -> x.ge(NewActivity::getEffectStartTime, date)
                            .le(NewActivity::getEffectEndTime, now));
                    return swp;
                })
                .ge(Objects.nonNull(queryVO.getEffectStartTime()), NewActivity::getEffectStartTime, queryVO.getEffectStartTime())
                .le(Objects.nonNull(queryVO.getEffectEndTime()), NewActivity::getEffectEndTime, queryVO.getEffectEndTime())
                .list();

        PageView<GoodsSignUpVO> pageView = new PageView<>();
        pageView.setPageNow((int) queryVO.getPageNow());
        pageView.setPageSize((int) queryVO.getPageSize());
        if((
                Objects.nonNull(queryVO.getActivityId())
                        || CollectionUtils.isNotEmpty(statusList)
                        || Objects.nonNull(queryVO.getEffectStartTime())
                        || Objects.nonNull(queryVO.getEffectEndTime())
                )
                && CollectionUtils.isEmpty(activityList)
        ){
            return pageView;
        }

        List<Long> activityIdList = activityList.stream().map(NewActivity::getId).collect(Collectors.toList());
        QueryWrapper<NewActivityGoods> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .select("DISTINCT goods_id")
                .in(CollectionUtils.isNotEmpty(activityIdList), "activity_id", activityIdList)
                .in(CollectionUtils.isNotEmpty(goodIdList), "goods_id", goodIdList);
        queryWrapper.orderByDesc("id");
        if (Objects.nonNull(queryVO.getSignUpStartTime())) {
            String format = queryVO.getSignUpStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            queryWrapper.ge("create_time", format);
        }
        if (Objects.nonNull(queryVO.getSignUpEndTime())) {
            String format = queryVO.getSignUpEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            queryWrapper.le("create_time", format);
        }
        // 0 - 待审核 1 - 初审通过 2 - 终审通过 3 - 初审落选 4 - 终审落选 5 - 全部
        List<Integer> auditList = queryVO.getAuditList();
        List<Integer> firstList = new ArrayList<>();
        List<Integer> lastList = new ArrayList<>();
        if(auditList.contains(1)){
            firstList.add(1);
        }
        if(auditList.contains(3)){
            firstList.add(0);
        }
        if(auditList.contains(0)){
            firstList.add(-1);
        }
        if(auditList.contains(2)){
            lastList.add(1);
        }
        if(auditList.contains(4)){
            lastList.add(0);
        }
        if(CollectionUtils.isNotEmpty(firstList)){
            queryWrapper.in("first_status", firstList);
        }
        if(CollectionUtils.isNotEmpty(lastList)){
            queryWrapper.in("status", lastList);
        }
        IPage<NewActivityGoods> page = newActivityGoodsService.getBaseMapper().selectPage(new Page<>(queryVO.getPageNow(), queryVO.getPageSize()), queryWrapper);
        pageView.setRowCount(page.getTotal());
        pageView.setPageCount(page.getPages());
        List<NewActivityGoods> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return pageView;
        }
        List<Long> goodsIdList = records.stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toList());

        LambdaQueryChainWrapper<NewActivityGoods> queryChainWrapper = newActivityGoodsService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(activityIdList), NewActivityGoods::getActivityId, activityIdList)
                .in(NewActivityGoods::getGoodsId, goodsIdList);
        if (Objects.nonNull(queryVO.getSignUpStartTime())) {
            String format = queryVO.getSignUpStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            queryChainWrapper.ge(NewActivityGoods::getCreateTime, format);
        }
        if (Objects.nonNull(queryVO.getSignUpEndTime())) {
            String format = queryVO.getSignUpEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            queryChainWrapper.le(NewActivityGoods::getCreateTime, format);
        }
        if(CollectionUtils.isNotEmpty(firstList)){
            queryChainWrapper.in(NewActivityGoods::getFirstStatus, firstList);
        }
        if(CollectionUtils.isNotEmpty(lastList)){
            queryChainWrapper.in(NewActivityGoods::getStatus, lastList);
        }
        List<NewActivityGoods> allList = queryChainWrapper.list();
        Map<Long, List<NewActivityGoods>> goodsMap = allList.stream().collect(Collectors.groupingBy(NewActivityGoods::getGoodsId, Collectors.toList()));
        List<Long> actList = goodsMap.values().stream().flatMap(List::stream).map(NewActivityGoods::getActivityId).distinct().collect(Collectors.toList());
        Collection<NewActivity> newActivities = newActivityService.listByIds(actList);
        Map<Long, NewActivity> activityMap = newActivities.stream().collect(Collectors.toMap(NewActivity::getId, Function.identity()));
        List<GoodsSignUpVO> collect = goodsMap.entrySet().stream()
                .map(entry -> {
                    GoodsSignUpVO goodsSignUpVO = new GoodsSignUpVO();
                    List<NewActivityGoods> value = entry.getValue();
                    String join1 = value.stream().map(e -> e.getActivityId() + "  " + e.getActivityName()).collect(Collectors.joining("\n"));
                    String join2 = value.stream().map(e -> {
                        Long activityId = e.getActivityId();
                        NewActivity newActivity = activityMap.get(activityId);
                        if(Objects.isNull(newActivity)){
                            return "活动已移除";
                        }
                        Date effectEndTime = newActivity.getEffectEndTime();
                        Date effectStartTime = newActivity.getEffectStartTime();
                        if (effectStartTime.compareTo(now) >= 0) {
                            return "未开始";
                        }
                        if (effectEndTime.compareTo(now) >= 0) {
                            return "进行中";
                        }
                        return "已结束";
                    }).collect(Collectors.joining("\n"));
                    String join3 = value.stream().map(e -> {
                        Integer status = e.getStatus();
                        Integer firstStatus = e.getFirstStatus();
                        if (!Objects.equals(status, -1)) {
                            return status == 1 ? "终审入选" : "终审落选";
                        }
                        if (Objects.equals(firstStatus, -1)) {
                            return "待审核";
                        }
                        return firstStatus == 1 ? "初审入选" : "初审落选";
                    }).collect(Collectors.joining("\n"));
                    goodsSignUpVO.setGoodId(entry.getKey());
                    String join4 = value.stream().map(e -> {
                        Long activityId = e.getActivityId();
                        NewActivity newActivity = activityMap.get(activityId);
                        if(Objects.isNull(newActivity)){
                            return "活动已移除";
                        }
                        Date effectStartTime = newActivity.getEffectStartTime();
                        Date effectEndTime = newActivity.getEffectEndTime();
                        return DateUtil.format(effectStartTime) + "-" + DateUtil.format(effectEndTime);
                    }).collect(Collectors.joining("\n"));
                    String join5 = value.stream().map(e -> DateUtil.format(e.getCreateTime())).collect(Collectors.joining("\n"));

                    goodsSignUpVO.setActivityDesc(join1);
                    goodsSignUpVO.setSignUpStatus(join2);
                    goodsSignUpVO.setAuditStatus(join3);
                    goodsSignUpVO.setEffectTime(join4);
                    goodsSignUpVO.setSignUpTime(join5);
                    return goodsSignUpVO;
                }).collect(Collectors.toList());
        pageView.setRecords(collect);
        return pageView;
    }

    void callDing(int count, String fileName, LocalDateTime now, Long activityId, AtomicInteger total, UserDTO operationUser, boolean processToEnd){

        String activityName = "";
        Integer type = 0;
        if(activityId != null){
            NewActivity newActivity = activityService.getById(activityId);
            if(newActivity != null){
                activityName = newActivity.getName();
                type = newActivity.getType();
            }
        }

        String content = (type == 1 ? "flashDeal活动批量导入报警：" : "运营选品批量导入报警：");
        content += "\n";
        content += "文件名：" + fileName;
        content += "\n";
        content += "活动名：" + activityName;
        content += "\n";
        content += "上传UTC时间：" + now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        content += "\n";
        if (processToEnd) {
            if (count > 0) {
                content += "1.活动商品执行成功，共" + total.get() + "商品，" + count + "个异常商品";
            } else {
                content += "1.活动商品执行成功，无异常商品";
            }
        }else {
            content += "1.活动商品执行失败，共处理" + total.get() + "商品，" + count + "个异常商品" + ", 文件解析异常中止";
        }
        content += "\n";
        content += "2.若是活动开始后导入，请手动【同步】活动\n";
        content += "3.操作人: " + operationUser.getName();
        log.info("DingDing: {}", content);
        dingDingCoreService.callDingDingSync(DingDingCoreServiceImpl.FILE_SIGN, content);
    }

    String uploadExcel(Long id) throws Exception{
        int pageNum = 1, pageSize = 1000;
        List<NewActivityGoods> newActivityGoodsList;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ExcelWriter writer = new ExcelWriterBuilder()
                .file(byteArrayOutputStream)
                .head(ActivityGoodsExport.class).build();
        WriteSheet writeSheet = new WriteSheet();
        do {
            newActivityGoodsList = newActivityGoodsService.lambdaQuery()
                    .eq(NewActivityGoods::getActivityId, id)
                    .eq(NewActivityGoods::getStatus, 1)
                    .orderByAsc(NewActivityGoods::getId)
                    .page(new Page<>(pageNum++, pageSize)).getRecords();
            if (CollectionUtils.isEmpty(newActivityGoodsList)) {
                log.info("asyncExport end {} {}", id, pageNum);
                break;
            }

            //店铺信息
            List<Long> shopIdList = newActivityGoodsList.stream().map(NewActivityGoods::getShopId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<com.voghion.marketing.model.po.FaMerchantsApply> faMerchantsApplyList = faMerchantsApplyService.lambdaQuery().in(com.voghion.marketing.model.po.FaMerchantsApply::getId, shopIdList).list();
            Map<Long, String> principalMap = org.apache.commons.collections4.CollectionUtils.emptyIfNull(faMerchantsApplyList).stream()
                    .filter(e -> org.apache.commons.lang3.StringUtils.isNotBlank(e.getPrincipal()) && Objects.nonNull(e.getId()))
                    .collect(Collectors.toMap(com.voghion.marketing.model.po.FaMerchantsApply::getId, com.voghion.marketing.model.po.FaMerchantsApply::getPrincipal));

            Set<Long> goodsIdList = newActivityGoodsList.stream().map(NewActivityGoods::getGoodsId).collect(Collectors.toSet());
            Set<Long> cateIds = newActivityGoodsList.stream().map(NewActivityGoods::getCategoryId).collect(Collectors.toSet());
            List<GoodsOutput> goods = goodsClientFactory.queryGoodsOnlyByGoodsIds(new ArrayList<>(goodsIdList)).getData();
            List<CategoryVO> categories = categoryClientFactory.queryCategoryByIds(new ArrayList<>(cateIds)).getData();
            Map<Long, CategoryVO> cateMap = categories.stream().collect(Collectors.toMap(CategoryVO::getId, Function.identity(), (a, b) -> b));
            Map<Long, GoodsOutput> goodsMap = goods.stream().collect(Collectors.toMap(GoodsOutput::getId, Function.identity(), (a, b) -> b));
            List<ActivityGoodsExport> collect = newActivityGoodsList.stream()
                    .map(ag -> {
                        ActivityGoodsExport ac = new ActivityGoodsExport();
                        GoodsOutput gds = goodsMap.get(ag.getGoodsId());
                        CategoryVO cate = cateMap.get(ag.getCategoryId());
                        if (gds != null) {
                            ac.setMinPrice(ag.getAfterDiscountMinPrice());
                            ac.setMaxPrice(ag.getAfterDiscountMaxPrice());
                            ac.setMinMarketPrice(gds.getMinMarketPrice() == null ? null : gds.getMinMarketPrice().multiply(ag.getDiscount()).setScale(2, BigDecimal.ROUND_UP));
                        }
                        ac.setIsShow(gds != null && "1".equals(gds.getIsShow()) ? "上架" : "下架");
                        ac.setPrincipal(principalMap.get(ag.getShopId()));
                        ac.setCategoryName(cate == null ? "" : cate.getName());
                        ac.setFirstLevelCategoryName(null == cate || null == cate.getFirstLevelCategory() ? "" : cate.getFirstLevelCategory().getName());
                        ac.setDiscount(ag.getDiscount());
                        ac.setCreateTime(DateUtil.format(ag.getCreateTime(), DateUtil.newFormat));
                        ac.setSort(ag.getSort());
                        ac.setStatusString(ag.getStatus() < 0 ? "待审核" : ag.getStatus() == 0 ? "落选" : "入选");
                        ac.setFirstStatus(ag.getFirstStatus() < 0 ? "待审核" : ag.getFirstStatus() == 0 ? "落选" : "入选");
                        ac.setGoodsName(ag.getGoodsName());
                        ac.setGoodsId(ag.getGoodsId());
                        ac.setShopName(ag.getShopName());
                        ac.setAuditor(ag.getAuditor());
                        ac.setAuditRemark(ag.getAuditRemark());
                        ac.setAuditTime(DateUtil.format(ag.getAuditTime()));
                        ac.setFirstAuditor(ag.getFirstAuditor());
                        ac.setFirstAuditRemark(ag.getFirstAuditRemark());
                        ac.setFirstAuditTime(DateUtil.format(ag.getFirstAuditTime()));
                        ac.setGoodsCreateTime(gds != null && gds.getCreateTime() != null ? gds.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
                        return ac;
                    }).collect(Collectors.toList());

            writer.write(collect, writeSheet);
        } while (true);
        writer.finish();
        return bussCommonFactory.uploadAndReturnUrl(byteArrayOutputStream.toByteArray(), "export_flashdeal_" + System.currentTimeMillis() + ".xlsx");
//        return GoogleUserConfig.domain + GoogleStorageUtil.putObjectAndReturnKey(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()), ObjectStorePathEnum.IMPORT_EXCEL, "xlsx");
    }

    long getTotal(Long shopId, Integer runDays, Integer sales) {
        try {
            CountRequest countRequest = new CountRequest("goods_every_day_es_a")
                    .query(
                            new BoolQueryBuilder()
                                    .must(QueryBuilders.termQuery("shopId", shopId))
                                    .must(QueryBuilders.termQuery("runDays", runDays))
                                    .must(QueryBuilders.termQuery("dealCnt", sales))
                    );
            return restHighLevelClient.count(countRequest, RequestOptions.DEFAULT).getCount();
        }catch (Throwable e){
            return 0;
        }
    }

    List<OutDbGoodsEveryDayVO> getOutDbGoodsEveryDayList(Long shopId, Integer runDays, Integer sales, int pageNow, int pageSize){
        SearchRequest source = new SearchRequest("goods_every_day_es_a")
                .source(new SearchSourceBuilder().query(new BoolQueryBuilder()
                                .must(QueryBuilders.termQuery("shopId", shopId))
                                .must(QueryBuilders.termQuery("runDays", runDays))
                                .must(QueryBuilders.termQuery("dealCnt", sales))
                        )
                        .from((pageNow - 1) * pageSize)
                        .size(pageSize)
                );
        List<OutDbGoodsEveryDayVO> goodsEveryDayVOList = baseEsQueryService.searchData(source, OutDbGoodsEveryDayVO.class);
        log.info("getOutDbGoodsEveryDayList shop id ={}, goodsEveryDayVOList = {}", shopId, JSON.toJSONString(goodsEveryDayVOList));
        return goodsEveryDayVOList;
    }

    @Transactional
    public void autoPassActivityGoods(Long activityId) {
        final Long[] id = {1L};
        RetryUtils.retry(() ->{
            List<NewActivityGoods> list = newActivityGoodsService.lambdaQuery()
                    .ge(NewActivityGoods::getId, id[0])
                    .eq(NewActivityGoods::getActivityId, activityId)
                    .eq(NewActivityGoods::getStatus, -1)
                    .eq(NewActivityGoods::getFirstStatus, 1)
                    .eq(NewActivityGoods::getAutoSelectStatus, 1)
                    .eq(NewActivityGoods::getIsDel, 0)
                    .orderByAsc(NewActivityGoods::getId)
                    .last("limit 500")
                    .list();
            if (CollectionUtils.isEmpty(list)) {
                return list;
            }
            id[0] = list.get(list.size() - 1).getId() + 1;
            Date date = new Date();
            list.forEach(goods -> {
                goods.setStatus(1);
                goods.setAuditRemark("自动审核");
                goods.setAuditor("自动审核");
                goods.setUpdateTime(date);
            });
            newActivityGoodsService.updateBatchById(list);
            //updateSelectedNums
            newActivityService.updateNewActivitySelectedNums(activityId, (long) list.size(),DigitalOperationsEnum.ADD);
            return list;
        }, CollectionUtils::isEmpty);

    }

    private void autoPassFullActivityGoods(Long activityId) {
        final Long[] id = {1L};
        RetryUtils.retry(() ->{
            List<ActivityReductionGoods> list = activityReductionGoodsService.lambdaQuery()
                    .ge(ActivityReductionGoods::getId, id[0])
                    .eq(ActivityReductionGoods::getActivityId, activityId)
                    .and(swap -> swap.eq(ActivityReductionGoods::getLastAuditorStatus, 0).or().isNull(ActivityReductionGoods::getLastAuditorStatus))
                    .eq(ActivityReductionGoods::getFirstAuditorStatus, 2)
                    .eq(ActivityReductionGoods::getAutoSelectStatus, 1)
                    .eq(ActivityReductionGoods::getIsDel, 0)
                    .orderByAsc(ActivityReductionGoods::getId)
                    .last("limit 500")
                    .list();
            if (CollectionUtils.isEmpty(list)) {
                return list;
            }
            id[0] = list.get(list.size() - 1).getId() + 1;
            LocalDateTime now = LocalDateTime.now();
            list.forEach(goods -> {
                goods.setLastAuditorStatus(2);
                goods.setLastAuditRemark("自动审核");
                goods.setLastAuditorUser("自动审核");
                goods.setLastAuditTime(now);
                goods.setUpdateTime(now);
            });
            activityReductionGoodsService.updateBatchById(list);
            return list;
        }, CollectionUtils::isEmpty);

    }

    /**
     * 记录虚拟商品列表同步或移除记录
     * @param items
     * @param syncInd
     * @param activityId
     * @param virGoodsId
     */
    private void recordSyncOrRemove(List<CustomListItems> items, Integer syncInd, Long activityId, Long virGoodsId){
        List<VirGoodsItemsSyncRecord> removeRecords = items.stream()
                .map(goods -> {
                    VirGoodsItemsSyncRecord virGoodsItemsRemoveRecord = new VirGoodsItemsSyncRecord();
                    virGoodsItemsRemoveRecord.setActivityId(activityId);
                    virGoodsItemsRemoveRecord.setVirGoodsId(virGoodsId);
                    virGoodsItemsRemoveRecord.setGoodsId(goods.getGoodsId());
                    virGoodsItemsRemoveRecord.setSyncInd(syncInd);
                    return virGoodsItemsRemoveRecord;
                }).collect(Collectors.toList());
        virGoodsItemsSyncRecordService.insertBatch(removeRecords);
    }

    //todo 临时逻辑 后续需要删除
    List<Long> specialShopList = Arrays.asList(
            1024898L, 1000003176L, 1000003173L, 1000003171L, 1000003156L, 1000003145L, 1000003132L,
            1000003119L, 1000003116L, 1000003113L, 1000003100L, 1000003084L, 1000003063L, 1000003027L,
            1000003020L, 1000003012L, 1000001143L, 1000001135L, 1000001144L, 1000001141L, 1000001136L,
            1000001137L, 1000001139L, 1000001140L, 1024897L, 1000003002L, 1024428L, 1024895L, 1024896L,
            1000001142L, 1000003000L, 1000003055L, 1000003110L, 1000003029L, 1000003004L, 1000003001L,
            1000003006L, 1000003053L, 1000003005L, 1000003010L, 1000003003L, 1000003017L, 1000003022L,
            1000003024L, 1000003033L, 1000003014L, 1000003069L, 1000003013L, 1000003035L, 1000003128L,
            1000003076L, 1000003141L, 1000003131L, 1000003021L, 1000003106L, 1000003018L, 1000003028L,
            1000003139L, 1000003039L, 1000003034L, 1000003042L, 1000003162L, 1000003047L, 1000003089L,
            1000003080L, 1000003143L, 1000003043L, 1000003026L, 1000003125L, 1000003051L, 1000003158L,
            1000003036L, 1000003048L, 1000003065L, 1000003096L, 1000003019L, 1000003046L, 1000003037L,
            1000003167L, 1000003177L, 1000003126L, 1000003015L, 1000003009L, 1000003129L, 1000003182L,
            1000003090L, 1000003083L, 1000003064L, 1000003054L, 1000003057L, 1000003137L, 1000003179L,
            1000003038L, 1000003023L, 1000003081L, 1000003098L, 1000003074L, 1000003153L, 1000003124L,
            1000003082L, 1000003061L, 1000003058L, 1000003109L, 1000003184L, 1000003086L, 1000003031L,
            1000003050L, 1000003114L, 1000003133L, 1000003011L, 1000003165L, 1000003105L, 1000003091L,
            1000003152L, 1000003079L, 1000003072L, 1000003062L, 1000003025L, 1000003044L, 1000003070L,
            1000003016L, 1000003183L, 1000003140L, 1000003052L, 1000003148L, 1000003136L, 1000003088L,
            1000003030L, 1000003008L, 1000003104L, 1000003191L, 1000003190L, 1000003189L, 1000003188L,
            1000003187L, 1000003186L, 1000003185L, 1000003181L, 1000003180L, 1000003178L, 1000003175L,
            1000003174L, 1000003172L, 1000003170L, 1000003169L, 1000003168L, 1000003166L, 1000003164L,
            1000003163L, 1000003161L, 1000003160L, 1000003159L, 1000003157L, 1000003155L, 1000003154L,
            1000003151L, 1000003150L, 1000003149L, 1000003147L, 1000003146L, 1000003144L, 1000003142L,
            1000003138L, 1000003135L, 1000003134L, 1000003130L, 1000003127L, 1000003123L, 1000003122L,
            1000003121L, 1000003120L, 1000003118L, 1000003117L, 1000003115L, 1000003112L, 1000003111L,
            1000003108L, 1000003107L, 1000003103L, 1000003102L, 1000003101L, 1000003099L, 1000003097L,
            1000003095L, 1000003094L, 1000003093L, 1000003092L, 1000003087L, 1000003085L, 1000003078L,
            1000003077L, 1000003075L, 1000003073L, 1000003071L, 1000003068L, 1000003067L, 1000003066L,
            1000003060L, 1000003059L, 1000003056L, 1000003049L, 1000003045L, 1000003041L, 1000003040L,
            1000003032L, 1000003007L
    );

    @Value("${check.self.shop.switch:false}")
    private Boolean checkSelfShopSwitch;
    /**
     * 校验店铺是否是自营 自营店铺不能报名活动
     */
    @Override
    public List<Long> checkIsSelfShop(Map<Long, Long> goodsShopMap) {

        //增加开关 通过开关判断是否走校验逻辑
        if (!checkSelfShopSwitch) {
            return null;
        }

        if (MapUtils.isEmpty(goodsShopMap)) {
            return null;
        }

        //判断是否是自营商品 并且 是负向商品
        List<Long> shopIds = goodsShopMap.values().stream().distinct().collect(Collectors.toList());

        Collection<com.voghion.marketing.model.po.FaMerchantsApply> faMerchantsApplies = faMerchantsApplyService.getSelfShopIdList(shopIds);
        List<Long> selfShopIds = faMerchantsApplies.stream().map(com.voghion.marketing.model.po.FaMerchantsApply::getId).collect(Collectors.toList());
        List<Long> selfGoodsIds = Lists.newArrayList();
        //获取满足条件的goodId
        goodsShopMap.forEach((k, v) -> {
            if (selfShopIds.contains(v) || specialShopList.contains(v)) {
                selfGoodsIds.add(k);
            }
        });
        if (CollectionUtils.isEmpty(selfShopIds)) {
            return null;
        }
        //判断是否是负向商品
        return goodsRemoteService.selectNegativeGoodsIds(selfGoodsIds);

    }


    //补全活动商品中的小二信息
    public void completePrincipalInfo(List<NewActivityGoods> activityGoods) {
        // 检查输入参数
        if (CollectionUtils.isEmpty(activityGoods)) {
            log.debug("Activity goods list is empty, skip principal info completion");
            return;
        }

        // 提取唯一的店铺ID
        List<Long> shopIds = activityGoods.stream()
                .map(NewActivityGoods::getShopId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(shopIds)) {
            log.debug("No valid shop IDs found, skip principal info completion");
            return;
        }

        // 查询店铺对应的小二信息
        List<FaMerchantsApplyDTO> faMerchantsApplyDTOS = goodsClientFactory.queryByShopIds(shopIds);
        if (CollectionUtils.isEmpty(faMerchantsApplyDTOS)) {
            log.debug("No principal info found for shop IDs: {}", shopIds);
            return;
        }

        Map<Long, String> shopIdPrincipalMap = faMerchantsApplyDTOS.stream()
                .filter(dto -> dto.getShopId() != null && StringUtils.isNotBlank(dto.getPrincipal()))
                .collect(Collectors.toMap(
                        FaMerchantsApplyDTO::getShopId,
                        FaMerchantsApplyDTO::getPrincipal,
                        (existing, replacement) -> existing
                ));

        if (MapUtils.isEmpty(shopIdPrincipalMap)) {
            log.debug("No valid principal mapping found");
            return;
        }

        // 补全活动商品的小二信息
        activityGoods.forEach(e -> {
            if (shopIdPrincipalMap.containsKey(e.getShopId())) {
                e.setPrincipal(shopIdPrincipalMap.get(e.getShopId()));
            }
        });

    }

    /**
     * 补全活动商品中小二的信息 todo 上线发布后删除  预计上线时间 8.3
     */
    @Override
    public void initActivityGoodsInfo() {


        // 处理NewActivityGoods表的小二信息更新
        processPrincipalInfoForActivityGoods();

        // 处理ActivityReductionGoods表的小二信息更新
        processPrincipalInfoForReductionGoods();

        log.info("initActivityGoodsInfo completed");
    }

    /**
     * 处理活动商品的小二信息更新
     */
    private void processPrincipalInfoForActivityGoods() {
        // 获取所有进行中的活动 线上统计30左右
        List<NewActivity> activities = newActivityService.lambdaQuery()
                .eq(NewActivity::getIsDel, 0)
                .ge(NewActivity::getEffectEndTime, new Date())
                .select(NewActivity::getId)
                .list();

        if (CollectionUtils.isEmpty(activities)) {
            log.info("No active activities found, initActivityGoodsInfo return");
            return;
        }

        List<Long> activityIds = activities.stream().map(NewActivity::getId).collect(Collectors.toList());
        log.info("Found {} active activities, activityIds: {}", activityIds.size(), activityIds);
        Integer pageNum = 1, pageSize = 100;
        IPage<NewActivityGoods> page;
        Date date = new Date();

        do {
            // 分页获取shopId
            page = newActivityGoodsService.lambdaQuery()
                    .select(NewActivityGoods::getShopId)
                    .eq(NewActivityGoods::getIsDel, 0)
                    .in(NewActivityGoods::getActivityId, activityIds)
                    .isNull(NewActivityGoods::getPrincipal)
                    .groupBy(NewActivityGoods::getShopId)
                    .page(new Page<>(pageNum, pageSize));

            List<Long> shopIds = page.getRecords().stream()
                    .map(NewActivityGoods::getShopId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            log.info("Processing shopIds batch, page: {}, size: {}", pageNum, shopIds.size());
            if (CollectionUtils.isNotEmpty(shopIds)) {
                // 获取店铺对应的小二信息
                List<FaMerchantsApplyDTO> faMerchantsApplyDTOS = goodsClientFactory.queryByShopIds(shopIds);

                if (CollectionUtils.isNotEmpty(faMerchantsApplyDTOS)) {
                    // 批量更新活动商品表中的小二信息字段
                    faMerchantsApplyDTOS.parallelStream().forEach(faMerchantsApplyDTO -> {
                        if (faMerchantsApplyDTO.getShopId() != null &&
                                StringUtils.isNotBlank(faMerchantsApplyDTO.getPrincipal())) {
                            boolean updateCount = newActivityGoodsService.lambdaUpdate()
                                    .eq(NewActivityGoods::getShopId, faMerchantsApplyDTO.getShopId())
                                    .in(NewActivityGoods::getActivityId, activityIds)
                                    .set(NewActivityGoods::getPrincipal, faMerchantsApplyDTO.getPrincipal())
                                    .set(NewActivityGoods::getUpdateTime, date)
                                    .update();
                            log.info("Updated principal for shopId: {}, updateCount: {}",
                                    faMerchantsApplyDTO.getShopId(), updateCount);
                        }
                    });
                }
            }

            pageNum++;

        } while (page.getCurrent() < page.getPages());
    }

    /**
     * 处理满减商品的小二信息更新
     */
    private void processPrincipalInfoForReductionGoods() {

        // 获取所有进行中的活动 线上统计30左右
        List<ActivityFullReduction> activities = activityFullReductionService.lambdaQuery()
                .eq(ActivityFullReduction::getIsDel, 0)
                .ge(ActivityFullReduction::getActivityEndTime, new Date())
                .select(ActivityFullReduction::getId)
                .list();

        if (CollectionUtils.isEmpty(activities)) {
            log.info("No active activities found, initActivityGoodsInfo return");
            return;
        }

        List<Long> activityIds = activities.stream().map(ActivityFullReduction::getId).collect(Collectors.toList());
        log.info("Found {} active activities, activityIds: {}", activityIds.size(), activityIds);

        IPage<ActivityReductionGoods> reductionGoodsIPage;
        Integer pageNum = 1;
        Integer pageSize = 100;
        Date date = new Date();
        do {
            // 分页获取shopId
            reductionGoodsIPage = activityReductionGoodsService.lambdaQuery()
                    .select(ActivityReductionGoods::getShopId)
                    .eq(ActivityReductionGoods::getIsDel, 0)
                    .in(ActivityReductionGoods::getActivityId, activityIds)
                    .isNull(ActivityReductionGoods::getPrincipal)
                    .groupBy(ActivityReductionGoods::getShopId)
                    .page(new Page<>(pageNum, pageSize));

            List<Long> shopIds = reductionGoodsIPage.getRecords().stream()
                    .map(ActivityReductionGoods::getShopId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            log.info("Processing shopIds batch, page: {}, size: {}", pageNum, shopIds.size());

            if (CollectionUtils.isNotEmpty(shopIds)) {
                // 获取店铺对应的小二信息
                List<FaMerchantsApplyDTO> faMerchantsApplyDTOS = goodsClientFactory.queryByShopIds(shopIds);

                if (CollectionUtils.isNotEmpty(faMerchantsApplyDTOS)) {
                    // 批量更新活动商品表中的小二信息字段
                    faMerchantsApplyDTOS.parallelStream().forEach(faMerchantsApplyDTO -> {
                        if (faMerchantsApplyDTO.getShopId() != null &&
                                StringUtils.isNotBlank(faMerchantsApplyDTO.getPrincipal())) {
                            boolean updateCount = activityReductionGoodsService.lambdaUpdate()
                                    .eq(ActivityReductionGoods::getShopId, faMerchantsApplyDTO.getShopId())
                                    .in(ActivityReductionGoods::getActivityId, activityIds)
                                    .set(ActivityReductionGoods::getPrincipal, faMerchantsApplyDTO.getPrincipal())
                                    .set(ActivityReductionGoods::getUpdateTime, date)
                                    .update();
                            log.info("Updated principal for shopId: {}, updateCount: {}",
                                    faMerchantsApplyDTO.getShopId(), updateCount);
                        }
                    });
                }
            }

            pageNum++;

        } while (reductionGoodsIPage.getCurrent() < reductionGoodsIPage.getPages());

    }

    /**
     * 处理未生效活动中重复报名商品数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleRepeatSignUpGoods(Long activityId) {
        List<NewActivity> activities = getTargetActivities(activityId);

        AtomicInteger totalCount = new AtomicInteger();

        activities.forEach(activity -> {
            log.info("Processing activity: {}", activity.getName());

            // 找出与当前活动有交集的其他活动
            List<Long> intersectActivityIds = findIntersectActivityIds(activity);
            log.info("Found {} intersect activities", intersectActivityIds.size());

            if (CollectionUtils.isNotEmpty(intersectActivityIds)) {
                processActivityGoods(activity, intersectActivityIds, totalCount);
            }
        });

        log.info("重复活动处理 总数据量: {}", totalCount.get());
    }

    /**
     * 获取目标活动列表
     */
    private List<NewActivity> getTargetActivities(Long activityId) {
//        if (Objects.nonNull(activityId)) {
        return activityService.lambdaQuery()
                .eq(NewActivity::getId, activityId)
                .eq(NewActivity::getIsDel, 0)
                .list();
//        } else {
//            // 获取所有未开始的活动
//            return activityService.lambdaQuery()
//                    .eq(NewActivity::getIsDel, 0)
//                    .ge(NewActivity::getEffectStartTime, new Date())
//                    .list();
//        }
    }

    /**
     * 查找与指定活动时间重叠的其他活动ID
     */
    private List<Long> findIntersectActivityIds(NewActivity activity) {
        return activityService.lambdaQuery()
                .eq(NewActivity::getIsDel, 0)  // 未删除的活动
                .ne(NewActivity::getId, activity.getId())  // 排除自己
                .and(q -> q
                        .lt(NewActivity::getEffectStartTime, activity.getEffectEndTime())  // 其他活动开始时间 < 当前活动结束时间
                        .gt(NewActivity::getEffectEndTime, activity.getEffectStartTime())   // 其他活动结束时间 > 当前活动开始时间
                )
                .list()
                .stream()
                .map(NewActivity::getId)
                .collect(Collectors.toList());
    }

    /**
     * 处理活动商品
     */
    private void processActivityGoods(NewActivity activity, List<Long> intersectActivityIds, AtomicInteger totalCount) {
        int pageNum = 1;
        int pageSize = 100;
        IPage<NewActivityGoods> activityGoodsPage;

        do {
            // 获取所有报名了当前活动的商品
            activityGoodsPage = newActivityGoodsService.lambdaQuery()
                    .eq(NewActivityGoods::getIsDel, 0)
                    .eq(NewActivityGoods::getActivityId, activity.getId())
//                    .eq(NewActivityGoods::getStatus, 1)
                    .select(NewActivityGoods::getGoodsId)
                    .page(new Page<>(pageNum, pageSize));

            log.info("Found {} activity goods in page {}", activityGoodsPage.getRecords().size(), pageNum);

            if (CollectionUtils.isNotEmpty(activityGoodsPage.getRecords())) {
                List<Long> existsGoodsIds = activityGoodsPage.getRecords()
                        .stream()
                        .map(NewActivityGoods::getGoodsId)
                        .collect(Collectors.toList());

                processIntersectGoods(existsGoodsIds, intersectActivityIds, totalCount);
                pageNum++;
            }
        } while (activityGoodsPage.getCurrent() < activityGoodsPage.getPages());
    }

    /**
     * 处理重叠活动商品
     */
    private void processIntersectGoods(List<Long> existsGoodsIds, List<Long> intersectActivityIds, AtomicInteger totalCount) {
        int pageNum = 1;
        int pageSize = 100;
        IPage<NewActivityGoods> repeateActivityGoodsIPage;

        do {
            repeateActivityGoodsIPage = newActivityGoodsService.lambdaQuery()
                    .eq(NewActivityGoods::getIsDel, 0)
                    .in(NewActivityGoods::getActivityId, intersectActivityIds)
                    .eq(NewActivityGoods::getStatus, 1)
                    .in(NewActivityGoods::getGoodsId, existsGoodsIds)
                    .page(new Page<>(pageNum, pageSize));

            log.info("Found {} intersect activity goods in page {}", repeateActivityGoodsIPage.getRecords().size(), pageNum);
            totalCount.addAndGet(repeateActivityGoodsIPage.getRecords().size());

            // 落选商品
            if (CollectionUtils.isNotEmpty(repeateActivityGoodsIPage.getRecords())) {
                List<Long> goodsIdsToReject = repeateActivityGoodsIPage.getRecords()
                        .stream()
                        .map(NewActivityGoods::getId)
                        .collect(Collectors.toList());

                ActivityGoodsSelectVo vo = new ActivityGoodsSelectVo();
                vo.setIds(goodsIdsToReject);
                vo.setSelect(0);
                vo.setRemark("数据批处理落选");

                this.selectOrLostActivityGoods(vo);
            }
            pageNum++;

        } while (repeateActivityGoodsIPage.getCurrent() < repeateActivityGoodsIPage.getPages());
    }

    /**
     * 查询商家可报名商品列表（预审功能）
     * 基于活动规则对商家商品进行预审核，返回符合条件的商品列表
     */
    @Override
    public PageView<AvailGoodsForShopVO> queryAvailGoodsForShop(AvailGoodsForShopQueryVO queryVO) {
        log.info("queryAvailGoodsForShop start, queryVO: {}", JSON.toJSONString(queryVO));

        try {
            // 1. 参数校验
            if (queryVO.getActivityId() == null || queryVO.getShopId() == null) {
                throw new CustomException(BusinessMarketingResultCode.PARAM_ERROR, "活动ID和商家ID不能为空");
            }

            // 2. 尝试从缓存获取数据（如果没有特殊筛选条件）
            if (!hasSpecialFilters(queryVO)) {
                PageView<AvailGoodsForShopVO> cachedResult = availGoodsCacheService.getAvailGoodsCache(
                        queryVO.getActivityId(), queryVO.getShopId());
                if (cachedResult != null) {
                    log.info("queryAvailGoodsForShop hit cache, activityId: {}, shopId: {}",
                            queryVO.getActivityId(), queryVO.getShopId());
                    return paginateResult(cachedResult.getRecords(), queryVO.getPageNow(), queryVO.getPageSize());
                }
            }

            // 3. 获取活动信息和配置
            NewActivity activity = newActivityService.getById(queryVO.getActivityId());
            if (activity == null) {
                throw new CustomException(BusinessMarketingResultCode.ACTIVITY_NOT_EXIST, "活动不存在");
            }

            AutoSelectConfig autoSelectConfig = autoSelectConfigService.lambdaQuery()
                    .eq(AutoSelectConfig::getType, activity.getType())
                    .eq(AutoSelectConfig::getTypeId, queryVO.getActivityId())
                    .one();

            if (autoSelectConfig == null) {
                log.warn("queryAvailGoodsForShop autoSelectConfig is null, activityId: {}", queryVO.getActivityId());
                return new PageView<>(Lists.newArrayList(), 0L, queryVO.getPageNow(), queryVO.getPageSize());
            }

            // 4. 查询商家商品列表
            PageView<AvailGoodsForShopVO> result = queryShopGoodsWithPreAudit(queryVO, activity, autoSelectConfig);

            // 5. 缓存结果（如果没有特殊筛选条件）
            if (!hasSpecialFilters(queryVO) && result.getTotal() <= 10000) {
                availGoodsCacheService.setAvailGoodsCache(queryVO.getActivityId(), queryVO.getShopId(), result);
            }

            log.info("queryAvailGoodsForShop end, result size: {}", result.getRecords().size());
            return result;

        } catch (Exception e) {
            log.error("queryAvailGoodsForShop error, queryVO: {}", JSON.toJSONString(queryVO), e);
            throw new CustomException(BusinessMarketingResultCode.SYSTEM_ERROR, "查询商家可报名商品列表失败");
        }
    }

    /**
     * 准备商家可报名商品列表（预审功能 - 缓存版本）
     */
    @Override
    public PageView<AvailGoodsForShopVO> prepareAvailGoodsForShop(AvailGoodsForShopQueryVO queryVO) {
        log.info("prepareAvailGoodsForShop start, queryVO: {}", JSON.toJSONString(queryVO));

        try {
            // 1. 参数校验
            if (queryVO.getActivityId() == null || queryVO.getShopId() == null) {
                throw new CustomException(BusinessMarketingResultCode.PARAM_ERROR, "活动ID和商家ID不能为空");
            }

            // 2. 检查每日缓存
            if (!Boolean.TRUE.equals(queryVO.getForceRefresh())) {
                PageView<AvailGoodsForShopVO> cachedResult = availGoodsCacheService.getDailyAvailGoodsCache(
                        queryVO.getActivityId(), queryVO.getShopId());
                if (cachedResult != null) {
                    log.info("prepareAvailGoodsForShop hit daily cache, activityId: {}, shopId: {}",
                            queryVO.getActivityId(), queryVO.getShopId());
                    return paginateResult(cachedResult.getRecords(), queryVO.getPageNow(), queryVO.getPageSize());
                }
            }

            // 3. 尝试获取分布式锁，避免重复处理
            if (!availGoodsCacheService.tryLock(queryVO.getActivityId(), queryVO.getShopId())) {
                // 如果获取锁失败，等待一段时间后再次尝试从缓存获取
                try {
                    Thread.sleep(1000);
                    PageView<AvailGoodsForShopVO> cachedResult = availGoodsCacheService.getDailyAvailGoodsCache(
                            queryVO.getActivityId(), queryVO.getShopId());
                    if (cachedResult != null) {
                        return paginateResult(cachedResult.getRecords(), queryVO.getPageNow(), queryVO.getPageSize());
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }

                log.warn("prepareAvailGoodsForShop failed to get lock, activityId: {}, shopId: {}",
                        queryVO.getActivityId(), queryVO.getShopId());
                throw new CustomException(BusinessMarketingResultCode.SYSTEM_BUSY, "系统繁忙，请稍后重试");
            }

            try {
                // 4. 获取活动信息
                NewActivity activity = newActivityService.getById(queryVO.getActivityId());
                if (activity == null) {
                    throw new CustomException(BusinessMarketingResultCode.ACTIVITY_NOT_EXIST, "活动不存在");
                }

                AutoSelectConfig autoSelectConfig = autoSelectConfigService.lambdaQuery()
                        .eq(AutoSelectConfig::getType, activity.getType())
                        .eq(AutoSelectConfig::getTypeId, queryVO.getActivityId())
                        .one();

                if (autoSelectConfig == null) {
                    log.warn("prepareAvailGoodsForShop autoSelectConfig is null, activityId: {}", queryVO.getActivityId());
                    return new PageView<>(Lists.newArrayList(), 0L, queryVO.getPageNow(), queryVO.getPageSize());
                }

                // 5. 查询全量商品并预审核
                AvailGoodsForShopQueryVO fullQueryVO = new AvailGoodsForShopQueryVO();
                fullQueryVO.setActivityId(queryVO.getActivityId());
                fullQueryVO.setShopId(queryVO.getShopId());
                fullQueryVO.setPageNow(1);
                fullQueryVO.setPageSize(10000); // 设置较大的页面大小获取全量数据

                PageView<AvailGoodsForShopVO> fullResult = queryShopGoodsWithPreAudit(fullQueryVO, activity, autoSelectConfig);

                // 6. 检查商品数量限制
                if (fullResult.getTotal() > 10000) {
                    log.warn("prepareAvailGoodsForShop goods count exceed limit, activityId: {}, shopId: {}, count: {}",
                            queryVO.getActivityId(), queryVO.getShopId(), fullResult.getTotal());

                    // 返回提示信息
                    AvailGoodsForShopVO warningVO = new AvailGoodsForShopVO();
                    warningVO.setCannotSignUpReason("商品数量超过1万件，请联系客服处理");
                    fullResult = new PageView<>(Lists.newArrayList(warningVO), 1L, 1, 1);
                }

                // 7. 缓存结果（缓存1天）
                availGoodsCacheService.setDailyAvailGoodsCache(queryVO.getActivityId(), queryVO.getShopId(), fullResult);

                // 8. 返回分页结果
                return paginateResult(fullResult.getRecords(), queryVO.getPageNow(), queryVO.getPageSize());

            } finally {
                // 9. 释放锁
                availGoodsCacheService.releaseLock(queryVO.getActivityId(), queryVO.getShopId());
            }

        } catch (Exception e) {
            log.error("prepareAvailGoodsForShop error, queryVO: {}", JSON.toJSONString(queryVO), e);
            throw new CustomException(BusinessMarketingResultCode.SYSTEM_ERROR, "准备商家可报名商品列表失败");
        }
    }

    /**
     * 检查商家可报名商品状态（预审功能 - 验证版本）
     */
    @Override
    public PageView<AvailGoodsForShopVO> checkAvailGoodsForShop(AvailGoodsForShopQueryVO queryVO) {
        log.info("checkAvailGoodsForShop start, queryVO: {}", JSON.toJSONString(queryVO));

        try {
            // 1. 参数校验
            if (queryVO.getActivityId() == null || queryVO.getShopId() == null) {
                throw new CustomException(BusinessMarketingResultCode.PARAM_ERROR, "活动ID和商家ID不能为空");
            }

            // 2. 获取活动信息
            NewActivity activity = newActivityService.getById(queryVO.getActivityId());
            if (activity == null) {
                throw new CustomException(BusinessMarketingResultCode.ACTIVITY_NOT_EXIST, "活动不存在");
            }

            AutoSelectConfig autoSelectConfig = autoSelectConfigService.lambdaQuery()
                    .eq(AutoSelectConfig::getType, activity.getType())
                    .eq(AutoSelectConfig::getTypeId, queryVO.getActivityId())
                    .one();

            if (autoSelectConfig == null) {
                log.warn("checkAvailGoodsForShop autoSelectConfig is null, activityId: {}", queryVO.getActivityId());
                return new PageView<>(Lists.newArrayList(), 0L, queryVO.getPageNow(), queryVO.getPageSize());
            }

            // 3. 如果指定了商品ID，只检查指定商品
            if (queryVO.getGoodsId() != null) {
                AvailGoodsForShopVO result = checkSingleGoodsPreAudit(queryVO.getGoodsId(), queryVO.getShopId(), activity, autoSelectConfig);
                return new PageView<>(Lists.newArrayList(result), 1L, 1, 1);
            }

            // 4. 否则进行常规查询
            return queryShopGoodsWithPreAudit(queryVO, activity, autoSelectConfig);

        } catch (Exception e) {
            log.error("checkAvailGoodsForShop error, queryVO: {}", JSON.toJSONString(queryVO), e);
            throw new CustomException(BusinessMarketingResultCode.SYSTEM_ERROR, "检查商家可报名商品状态失败");
        }
    }

    /**
     * 查询商家商品并进行预审核
     */
    private PageView<AvailGoodsForShopVO> queryShopGoodsWithPreAudit(AvailGoodsForShopQueryVO queryVO,
                                                                     NewActivity activity,
                                                                     AutoSelectConfig autoSelectConfig) {
        // 1. 构建ES查询条件
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("shopId", queryVO.getShopId()))
                .must(QueryBuilders.termQuery("isShow", "1")) // 商品必须上架
                .must(QueryBuilders.termQuery("isDel", 0)); // 商品未删除

        // 2. 添加商品名称模糊搜索
        if (StringUtils.isNotBlank(queryVO.getGoodsName())) {
            boolQuery.must(QueryBuilders.matchQuery("name", queryVO.getGoodsName()));
        }

        // 3. 添加类目过滤
        if (queryVO.getCategoryId() != null) {
            boolQuery.must(QueryBuilders.termQuery("categoryId", queryVO.getCategoryId()));
        }

        // 4. 添加品牌过滤
        if (queryVO.getBrandId() != null) {
            boolQuery.must(QueryBuilders.termQuery("brandId", queryVO.getBrandId()));
        }

        // 5. 添加价格范围过滤
        if (queryVO.getMinPrice() != null || queryVO.getMaxPrice() != null) {
            BoolQueryBuilder priceQuery = QueryBuilders.boolQuery();
            if (queryVO.getMinPrice() != null) {
                priceQuery.must(QueryBuilders.rangeQuery("minPrice").gte(queryVO.getMinPrice()));
            }
            if (queryVO.getMaxPrice() != null) {
                priceQuery.must(QueryBuilders.rangeQuery("maxPrice").lte(queryVO.getMaxPrice()));
            }
            boolQuery.must(priceQuery);
        }

        // 6. 执行ES查询
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQuery)
                .from((int) ((queryVO.getPageNow() - 1) * queryVO.getPageSize()))
                .size((int) queryVO.getPageSize());

        // 7. 添加排序
        if (StringUtils.isNotBlank(queryVO.getSortField())) {
            boolean isAsc = !"desc".equalsIgnoreCase(queryVO.getSortDirection());
            switch (queryVO.getSortField()) {
                case "createTime":
                    sourceBuilder.sort("createTime", isAsc ? org.opensearch.search.sort.SortOrder.ASC : org.opensearch.search.sort.SortOrder.DESC);
                    break;
                case "sales":
                    sourceBuilder.sort("sales", isAsc ? org.opensearch.search.sort.SortOrder.ASC : org.opensearch.search.sort.SortOrder.DESC);
                    break;
                case "price":
                    sourceBuilder.sort("minPrice", isAsc ? org.opensearch.search.sort.SortOrder.ASC : org.opensearch.search.sort.SortOrder.DESC);
                    break;
                default:
                    sourceBuilder.sort("createTime", org.opensearch.search.sort.SortOrder.DESC);
            }
        } else {
            sourceBuilder.sort("createTime", org.opensearch.search.sort.SortOrder.DESC);
        }

        List<GoodsESVo> goodsESVos = goodsEsService.searchGoods(sourceBuilder);
        long total = goodsEsService.countGoods(boolQuery);

        // 8. 转换为VO并进行预审核
        List<AvailGoodsForShopVO> resultList = goodsESVos.stream()
                .map(goodsESVo -> convertToAvailGoodsVO(goodsESVo, activity, autoSelectConfig))
                .collect(Collectors.toList());

        return new PageView<>(resultList, total, queryVO.getPageNow(), queryVO.getPageSize());
    }

    /**
     * 检查单个商品的预审核状态
     */
    private AvailGoodsForShopVO checkSingleGoodsPreAudit(Long goodsId, Long shopId,
                                                         NewActivity activity,
                                                         AutoSelectConfig autoSelectConfig) {
        // 1. 从ES获取商品信息
        List<GoodsESVo> goodsESVos = goodsEsService.queryGoodsByGoodsIds(Lists.newArrayList(goodsId));
        if (CollectionUtils.isEmpty(goodsESVos)) {
            throw new CustomException(BusinessMarketingResultCode.GOODS_NOT_EXIST, "商品不存在");
        }

        GoodsESVo goodsESVo = goodsESVos.get(0);
        if (!shopId.equals(goodsESVo.getShopId())) {
            throw new CustomException(BusinessMarketingResultCode.PARAM_ERROR, "商品不属于该商家");
        }

        // 2. 转换为VO并进行预审核
        return convertToAvailGoodsVO(goodsESVo, activity, autoSelectConfig);
    }

    /**
     * 将GoodsESVo转换为AvailGoodsForShopVO并进行预审核
     */
    private AvailGoodsForShopVO convertToAvailGoodsVO(GoodsESVo goodsESVo,
                                                      NewActivity activity,
                                                      AutoSelectConfig autoSelectConfig) {
        AvailGoodsForShopVO vo = new AvailGoodsForShopVO();

        // 1. 基础商品信息
        vo.setGoodsId(goodsESVo.getId());
        vo.setGoodsName(goodsESVo.getName());
        vo.setMainImage(goodsESVo.getMainImage());
        vo.setItemNumber(goodsESVo.getItemNumber());
        vo.setCategoryId(goodsESVo.getCategoryId());
        vo.setShopId(goodsESVo.getShopId());
        vo.setShopName(goodsESVo.getShopName());
        vo.setMinPrice(goodsESVo.getMinPrice());
        vo.setMaxPrice(goodsESVo.getMaxPrice());
        vo.setMinMarketPrice(goodsESVo.getMinMarketPrice());
        vo.setMaxMarketPrice(goodsESVo.getMaxMarketPrice());
        vo.setMinGrouponPrice(goodsESVo.getMinGrouponPrice());
        vo.setMaxGrouponPrice(goodsESVo.getMaxGrouponPrice());
        vo.setSales(goodsESVo.getSales());
        vo.setCreateTime(goodsESVo.getCreateTime());

        // 2. 商品状态
        vo.setGoodsStatus("1".equals(goodsESVo.getIsShow()) ? 1 : 0);
        vo.setGoodsStatusDesc("1".equals(goodsESVo.getIsShow()) ? "上架" : "下架");
        vo.setHasStock(true); // 默认有库存，实际需要调用库存服务

        // 3. 检查是否已报名当前活动
        checkSignUpStatus(vo, activity.getId());

        // 4. 进行预审核
        performPreAudit(vo, activity, autoSelectConfig);

        return vo;
    }

    /**
     * 检查商品报名状态
     */
    private void checkSignUpStatus(AvailGoodsForShopVO vo, Long activityId) {
        NewActivityGoods existingGoods = newActivityGoodsService.lambdaQuery()
                .eq(NewActivityGoods::getActivityId, activityId)
                .eq(NewActivityGoods::getGoodsId, vo.getGoodsId())
                .eq(NewActivityGoods::getIsDel, 0)
                .one();

        if (existingGoods != null) {
            vo.setIsSignedUp(true);
            vo.setSignUpStatus(1);
            vo.setSignUpTime(existingGoods.getCreateTime().toLocalDateTime());
            vo.setActivityAuditStatus(existingGoods.getStatus());

            switch (existingGoods.getStatus()) {
                case 1:
                    vo.setActivityAuditStatusDesc("入选");
                    break;
                case 0:
                    vo.setActivityAuditStatusDesc("落选");
                    break;
                case -1:
                default:
                    vo.setActivityAuditStatusDesc("待审核");
                    break;
            }
        } else {
            vo.setIsSignedUp(false);
            vo.setSignUpStatus(0);
            vo.setActivityAuditStatus(-1);
            vo.setActivityAuditStatusDesc("未报名");
        }
    }

    /**
     * 执行预审核逻辑
     */
    private void performPreAudit(AvailGoodsForShopVO vo, NewActivity activity, AutoSelectConfig autoSelectConfig) {
        List<String> failReasons = Lists.newArrayList();
        boolean canSignUp = true;

        try {
            // 1. 基础商品状态检查
            if (vo.getGoodsStatus() != 1) {
                failReasons.add("商品未上架");
                canSignUp = false;
            }

            if (!vo.getHasStock()) {
                failReasons.add("商品无库存");
                canSignUp = false;
            }

            // 2. 检查是否已参与其他进行中的活动
            if (checkGoodsInOtherActivity(vo.getGoodsId(), activity.getId())) {
                failReasons.add("商品正在参与其他活动");
                canSignUp = false;
            }

            // 3. 活动基础要求检查
            if (!checkActivityBasicRequirements(vo, activity)) {
                failReasons.add("不符合活动基础要求");
                canSignUp = false;
            }

            // 4. 使用现有的自动审核逻辑进行详细检查
            if (canSignUp && autoSelectConfig != null) {
                ActivityFirstSelectDTO selectResult = performDetailedPreAudit(vo, activity, autoSelectConfig);
                if (selectResult != null) {
                    failReasons.add(selectResult.getDesc());
                    canSignUp = false;
                }
            }

            // 5. 设置预审核结果
            vo.setPreAuditStatus(canSignUp ? 1 : 0);
            vo.setPreAuditStatusDesc(canSignUp ? "通过" : "未通过");
            vo.setCanSignUp(canSignUp && !vo.getIsSignedUp());
            vo.setPreAuditFailReasons(failReasons);

            if (!failReasons.isEmpty()) {
                vo.setPreAuditFailReason(String.join("; ", failReasons));
            }

            if (!canSignUp) {
                vo.setCannotSignUpReason(vo.getPreAuditFailReason());
            } else if (vo.getIsSignedUp()) {
                vo.setCannotSignUpReason("商品已报名该活动");
            }

            vo.setPreAuditTime(LocalDateTime.now());

        } catch (Exception e) {
            log.error("performPreAudit error, goodsId: {}, activityId: {}", vo.getGoodsId(), activity.getId(), e);
            vo.setPreAuditStatus(0);
            vo.setPreAuditStatusDesc("预审核异常");
            vo.setCanSignUp(false);
            vo.setCannotSignUpReason("预审核系统异常，请稍后重试");
            vo.setPreAuditFailReason("系统异常");
        }
    }

    /**
     * 检查商品是否参与其他进行中的活动
     */
    private boolean checkGoodsInOtherActivity(Long goodsId, Long currentActivityId) {
        // 查询商品是否参与其他进行中的活动
        List<NewActivityGoods> otherActivityGoods = newActivityGoodsService.lambdaQuery()
                .eq(NewActivityGoods::getGoodsId, goodsId)
                .ne(NewActivityGoods::getActivityId, currentActivityId)
                .eq(NewActivityGoods::getIsDel, 0)
                .in(NewActivityGoods::getStatus, Lists.newArrayList(1, -1)) // 入选或待审核
                .list();

        if (CollectionUtils.isEmpty(otherActivityGoods)) {
            return false;
        }

        // 检查这些活动是否正在进行中
        List<Long> activityIds = otherActivityGoods.stream()
                .map(NewActivityGoods::getActivityId)
                .distinct()
                .collect(Collectors.toList());

        List<NewActivity> ongoingActivities = newActivityService.lambdaQuery()
                .in(NewActivity::getId, activityIds)
                .eq(NewActivity::getActivityStatus, 20) // 进行中状态
                .eq(NewActivity::getIsDel, 0)
                .list();

        return CollectionUtils.isNotEmpty(ongoingActivities);
    }

    /**
     * 检查活动基础要求
     */
    private boolean checkActivityBasicRequirements(AvailGoodsForShopVO vo, NewActivity activity) {
        // 1. 检查类目要求
        if (activity.getCatType() != null && activity.getCatType() != 0) {
            if (StringUtils.isNotBlank(activity.getCateCol())) {
                List<String> categoryIds = Arrays.asList(activity.getCateCol().split(","));
                String goodsCategoryId = String.valueOf(vo.getCategoryId());

                if (activity.getCatType() == 1) { // 限定类目
                    if (!categoryIds.contains(goodsCategoryId)) {
                        return false;
                    }
                } else if (activity.getCatType() == 2) { // 排除类目
                    if (categoryIds.contains(goodsCategoryId)) {
                        return false;
                    }
                }
            }
        }

        // 2. 检查品牌要求（如果有相关配置）
        // TODO: 根据实际业务需求添加品牌检查逻辑

        // 3. 检查商品创建时间要求（如果有相关配置）
        // TODO: 根据实际业务需求添加创建时间检查逻辑

        return true;
    }

    /**
     * 执行详细的预审核检查（复用现有的自动审核逻辑）
     */
    private ActivityFirstSelectDTO performDetailedPreAudit(AvailGoodsForShopVO vo,
                                                           NewActivity activity,
                                                           AutoSelectConfig autoSelectConfig) {
        try {
            // 1. 构建ActivityGoods对象
            ActivityGoods activityGoods = new ActivityGoods();
            activityGoods.setActivityType(1);
            activityGoods.setActivityId(activity.getId());
            activityGoods.setGoodsName(vo.getGoodsName());
            activityGoods.setGoodsId(vo.getGoodsId());
            activityGoods.setPrice(vo.getMaxPrice());
            activityGoods.setCategoryId(vo.getCategoryId());
            activityGoods.setImageUrl(vo.getMainImage());

            // 2. 构建检查参数
            ActivityGoodsSelectCheckParam checkParam = new ActivityGoodsSelectCheckParam();
            checkParam.setAutoSelectConfig(autoSelectConfig);
            checkParam.setActivityGoods(activityGoods);

            // 3. 获取必要的数据
            List<GoodsESVo> goodsESVos = goodsEsService.queryGoodsByGoodsIds(Lists.newArrayList(vo.getGoodsId()));
            if (CollectionUtils.isNotEmpty(goodsESVos)) {
                checkParam.setGoodsEsVoMap(goodsESVos.stream().collect(Collectors.toMap(GoodsESVo::getId, Function.identity())));
            }

            // 4. 获取商品扩展配置
            Map<Long, GoodsExtConfigModel> goodsExtConfigModelMap = ggetGodsExtConfigModelMap(autoSelectConfig,
                    Lists.newArrayList(createMockActivityGoods(vo, activity.getId())));
            checkParam.setGoodsExtConfigModelMap(goodsExtConfigModelMap);

            // 5. 执行检查
            return activityGoodsSelectCheckExecutor.check(checkParam);

        } catch (Exception e) {
            log.error("performDetailedPreAudit error, goodsId: {}, activityId: {}", vo.getGoodsId(), activity.getId(), e);
            ActivityFirstSelectDTO errorResult = new ActivityFirstSelectDTO();
            errorResult.setDesc("预审核检查异常");
            return errorResult;
        }
    }

    /**
     * 创建模拟的NewActivityGoods对象用于预审核
     */
    private NewActivityGoods createMockActivityGoods(AvailGoodsForShopVO vo, Long activityId) {
        NewActivityGoods mockGoods = new NewActivityGoods();
        mockGoods.setGoodsId(vo.getGoodsId());
        mockGoods.setActivityId(activityId);
        mockGoods.setGoodsName(vo.getGoodsName());
        mockGoods.setMainImage(vo.getMainImage());
        mockGoods.setCategoryId(vo.getCategoryId());
        mockGoods.setShopId(vo.getShopId());
        mockGoods.setShopName(vo.getShopName());
        mockGoods.setMinPrice(vo.getMinPrice());
        mockGoods.setMaxPrice(vo.getMaxPrice());
        mockGoods.setAfterDiscountMinPrice(vo.getMinPrice());
        mockGoods.setAfterDiscountMaxPrice(vo.getMaxPrice());
        mockGoods.setGoodsCreateTime(vo.getCreateTime() != null ?
                Timestamp.valueOf(vo.getCreateTime()) : new Timestamp(System.currentTimeMillis()));
        mockGoods.setIsShow(vo.getGoodsStatus() == 1 ? "1" : "0");
        return mockGoods;
    }

    /**
     * 对结果进行分页处理
     */
    private PageView<AvailGoodsForShopVO> paginateResult(List<AvailGoodsForShopVO> allResults,
                                                         long pageNow,
                                                         long pageSize) {
        if (CollectionUtils.isEmpty(allResults)) {
            return new PageView<>(Lists.newArrayList(), 0L, pageNow, pageSize);
        }

        long total = allResults.size();
        int startIndex = (int) ((pageNow - 1) * pageSize);
        int endIndex = (int) Math.min(startIndex + pageSize, total);

        if (startIndex >= total) {
            return new PageView<>(Lists.newArrayList(), total, pageNow, pageSize);
        }

        List<AvailGoodsForShopVO> pageResults = allResults.subList(startIndex, endIndex);
        return new PageView<>(pageResults, total, pageNow, pageSize);
    }

    /**
     * 检查是否有特殊筛选条件
     */
    private boolean hasSpecialFilters(AvailGoodsForShopQueryVO queryVO) {
        return StringUtils.isNotBlank(queryVO.getGoodsName()) ||
               queryVO.getGoodsId() != null ||
               queryVO.getCategoryId() != null ||
               queryVO.getBrandId() != null ||
               queryVO.getMinPrice() != null ||
               queryVO.getMaxPrice() != null ||
               queryVO.getPreAuditStatus() != null ||
               StringUtils.isNotBlank(queryVO.getSortField());
    }


}
