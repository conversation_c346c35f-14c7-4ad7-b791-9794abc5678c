package com.voghion.marketing.core;

import com.colorlight.base.model.PageView;
import com.voghion.marketing.api.dto.*;
import com.voghion.marketing.listener.ActivityGoodsSignUpByImportVo;
import com.voghion.marketing.listener.ActivityGoodsSortVo;
import com.voghion.marketing.model.dto.*;
import com.voghion.marketing.model.po.AutoSelectConfig;
import com.voghion.marketing.model.po.NewActivity;
import com.voghion.marketing.model.po.NewActivityGoods;
import com.voghion.marketing.model.vo.ActivityShopLimitVO;
import com.voghion.marketing.model.vo.AvailGoodsForShopQueryVO;
import com.voghion.marketing.model.vo.AvailGoodsForShopVO;
import com.voghion.marketing.model.vo.GoodsSignUpQueryVO;
import com.voghion.marketing.model.vo.GoodsSignUpVO;
import com.voghion.marketing.model.vo.activity.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface NewActivityGoodsCoreService {


    PageView<ActivityGoodsResult> queryActivityGoodsResult(ActivityGoodsVO vo);

    List<NewActivityGoodsDataDTO> queryActivityGoodsData(NewActivityGoodsDataDTO dto);

    ActivityGoodsSignUpResultVO signUpByOperator(ActivityGoodsSignUpVo vo, Boolean isCheck);

    Boolean signUpByShopKeeper(ActivityGoodsSignUpVo vo);

    void batchSighUpByTemplate(Long activityId, List<ActivityGoodsSignUpByImportVo> list);

    void importActivityGoodsSort(Long activityId, List<ActivityGoodsSortVo> list);


    Boolean selectOrLostActivityGoods(ActivityGoodsSelectVo vo);

    NewHomeActivityDataDTO queryHomeActivityData(NewActivityGoodsDataDTO dto);


    PageView<NewGoodsOnlyDTO> queryGoodsByOption(NewActivityGoodsDataDTO dto);

    PageView<ActivityGoodsResult> queryActivityGoodsPageForShop(ActivityGoodsVO vo);

    List<ActivityGoodsExport> exportActivityGoods(ActivityGoodsVO vo);

    /**
     * 异步导出活动管理-商品导出
     * @param vo
     */
    void asyncExportActivityGoods(ActivityGoodsVO vo);

    List<ActivityGoodsShopExport> exportActivityGoodsForShop(ActivityGoodsVO activityGoodsVO);

    List<NewHomeActivityDataDTO> queryHomeActivityDataList(NewActivityGoodsDataDTO dto);

    void updateActivityGoodsMinPrice(ActivityGoodsGoodsUpdatePriceDto dto);

    /**
     * 查询初审的分页数据
     *
     * @param activityGoodsVO
     * @return
     */
    PageView<ActivityGoodsResult> queryFirstActivityGoodsPage(ActivityGoodsVO activityGoodsVO);

    /**
     * 初审入选落选
     *
     * @param vo
     * @return
     */
    Boolean firstSelectOrLostActivityGoods(ActivityGoodsSelectVo vo);

    List<NewActivityGoods> queryCurrentApplyGoodsIds(Integer size, Long nowId);

    void updateBatchById(List<ActivityGoodsDTO> list);

    /**
     * 更新排序字段
     * @param vo
     * @return
     */
    Boolean updateActivityGoods(ActivityGoodsVO vo);

    /**
     * 活动和虚拟商品列表id绑定
     * @param bindVirGoodsIdVO
     * @return
     */
     Boolean bindVirGoodsItemsId(BindVirGoodsIdVO bindVirGoodsIdVO);

    /**
     * 移除虚拟商品列表
     * @param syncVirGoodsIdVO
     * @return
     */
    Boolean removeVirGoodsItems(SyncVirGoodsIdVO syncVirGoodsIdVO);

    /**
     * 同步虚拟商品列表
     * @param syncVirGoodsIdVO
     * @return
     */
    Boolean syncVirGoodsItems(SyncVirGoodsIdVO syncVirGoodsIdVO);

    /**
     * 自动同步所有活动虚拟商品列表
     * @return
     */
    Boolean autoSyncAllActivityVirGoodsItems(List<NewActivity> newActivities);

    /**
     * 自动移除所有活动虚拟商品列表
     * @return
     */
    Boolean autoRemoveAllActivityVirGoodsItems(List<NewActivity> newActivities);

    /**
     * 查询商品参与的活动简要信息
     *
     * @param goodsId
     * @return
     */
    List<ActivityGoodsBriefDTO> listActivityBriefInfoByGoodsId(List<Long> goodsId);

    /**
     * 批量报名活动，且通过初审
     * @param activityBatchSignUpDTO
     */
    void batchSignUpAndPassFirstProcess(ActivityBatchSignUpDTO activityBatchSignUpDTO);

    /**
     * 自动审批机申通过和小二审批通过的活动商品
     */
    void autoPassActivityGoods();

    void setAutoSelect(List<NewActivityGoods> activityGoods, Long activityId, AutoSelectConfig autoSelectConfig, Set<Long> whiteShopIdSetFromShopTag);

    void setAutoSelect();


    PageView<GoodsDTO> pageSignUpGoods(ActivityShopLimitVO vo);
    PageView<Long> pageGoods(GoodsCostDTO goodsCostDTO);

    void asyncExport(Long id);

    void removeEffectActivityGoods(List<Long> idList);

    /**
     * 查询商品正在参与进行中的活动集合
     */
    List<ActivityGoodsSimpleResult> queryCurrentActivityByGoodsId(List<Long> goodsIdList);
    void refreshNewActivityGoods();

    List<String> listFailReason(String failReason);

    void deleteCustomItems(Long customId, List<Long> goodsIdList, Long activityId);

    void signByFile(MultipartFile file);

    PageView<GoodsSignUpVO> pageGoodsSignUpVO(GoodsSignUpQueryVO queryVO);
    void checkDup(Long activityId);

    void handSelect(Long activityId);

    List<Long> checkIsSelfShop(Map<Long, Long> goodsShopMap);

    void initActivityGoodsInfo();

    void handleRepeatSignUpGoods(Long activityId);

    /**
     * 获取历史报名活动记录 结束6个月以上的
     * @param vo
     * @return
     */
    PageView<ActivityGoodsResult> pageHistoryActivityGoodsRecords(ActivityGoodsVO vo);

    /**
     * 获取活动报名记录
     * @param vo
     * @return
     */
    PageView<ActivityGoodsResult> pageActivityGoodsRecords(ActivityGoodsVO vo);
}
