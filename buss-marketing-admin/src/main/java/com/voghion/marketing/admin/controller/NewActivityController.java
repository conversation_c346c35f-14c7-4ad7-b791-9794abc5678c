package com.voghion.marketing.admin.controller;

import com.alibaba.excel.EasyExcel;
import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.colorlight.base.model.constants.NoLogin;
import com.voghion.marketing.core.NewActivityConfigCoreService;
import com.voghion.marketing.core.NewActivityCoreService;
import com.voghion.marketing.core.NewActivityGoodsCoreService;
import com.voghion.marketing.excel.model.ActivityGoodsSignUpVO;
import com.voghion.marketing.listener.ActivityGoodsSignUpByImportVo;
import com.voghion.marketing.listener.ActivityGoodsSortImportListener;
import com.voghion.marketing.listener.ActivityGoodsSortVo;
import com.voghion.marketing.listener.ActivitySignUpImportListener;
import com.voghion.marketing.model.dto.*;
import com.voghion.marketing.model.vo.*;
import com.voghion.marketing.model.vo.activity.ActivityGoodsVO;
import com.voghion.marketing.model.vo.activity.ActivityVO;
import com.voghion.marketing.model.vo.activity.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/newActivity")
@Api(tags = "活动信息")
@Slf4j
public class NewActivityController {

    @Resource
    private NewActivityCoreService activityCoreService;

    @Resource
    private NewActivityGoodsCoreService activityGoodsCoreService;

    @Resource
    private NewActivityConfigCoreService activityConfigCoreService;

    /**
     * 根据分页条件查询活动
     *
     * @param activityVO
     * @return
     */
    @PostMapping("/activityList")
    @ApiOperation(value = "查询活动列表")
    public Result<PageView<ActivityDTO>> queryActivityByPage(@RequestBody ActivityVO activityVO) {
        return Result.success(activityCoreService.queryActivityByPage(activityVO,null));
    }


    /**
     * 根据分页条件查询活动
     *
     * @param activityVO
     * @return
     */
    @PostMapping("/activityListForShop")
    @ApiOperation(value = "商家查询活动列表")
    public Result<PageView<ActivityDTO>> queryActivityByPageForShop(@RequestBody ActivityVO activityVO) {
        activityVO.setShop(true);
        return Result.success(activityCoreService.queryActivityByPageForShop(activityVO));
    }

    @PostMapping("/availGoodsForShop")
    @ApiOperation(value = "商家可报名商品列表", notes = "查询商家可报名的商品列表，支持分页和筛选条件")
    public Result<PageView<AvailGoodsForShopVO>> queryAvailGoodsForShop(@RequestBody AvailGoodsForShopQueryVO queryVO) {
        // 设置商家标识，确保只能查询自己的商品
        queryVO.setShop(true);
        PageView<AvailGoodsForShopVO> result = activityGoodsCoreService.queryAvailGoodsForShop(queryVO);
        return Result.success(result);
    }

    @PostMapping("/availGoodsForShop/prepare")
    @ApiOperation(value = "预处理商家可报名商品列表",
                  notes = "为商家预先生成全量可报名商品列表并缓存，缓存时间为一天。当商品数量超过1万时不展示具体商品")
    public Result<PageView<AvailGoodsForShopVO>> prepareAvailGoodsForShop(@RequestBody AvailGoodsForShopQueryVO queryVO) {
        // 设置商家标识，确保只能查询自己的商品
        queryVO.setShop(true);
        PageView<AvailGoodsForShopVO> result = activityGoodsCoreService.prepareAvailGoodsForShop(queryVO);
        return Result.success(result);
    }

    @PostMapping("/availGoodsForShop/check")
    @ApiOperation(value = "检查商家商品预审状态", notes = "实时检查指定商品是否符合活动报名条件")
    public Result<PageView<AvailGoodsForShopVO>> checkAvailGoodsForShop(@RequestBody AvailGoodsForShopQueryVO queryVO) {
        // 设置商家标识，确保只能查询自己的商品
        queryVO.setShop(true);
        PageView<AvailGoodsForShopVO> result = activityGoodsCoreService.checkAvailGoodsForShop(queryVO);
        return Result.success(result);
    }


    @PostMapping("/first/activityGoodsList")
    @ApiOperation(value = "查询活动商品列表")
    @NoLogin
    public Result<PageView<ActivityGoodsResult>> queryFirstActivityGoodsPage(@RequestBody ActivityGoodsVO activityGoodsVO){
        return Result.success(activityGoodsCoreService.queryFirstActivityGoodsPage(activityGoodsVO));
    }


    @PostMapping("/activityGoodsList")
    @ApiOperation(value = "查询活动商品列表")
    @NoLogin
    public Result<PageView<ActivityGoodsResult>> queryActivityGoodsPage(@RequestBody ActivityGoodsVO activityGoodsVO){
//        //终审只能看见 初审 入选的商品
//        activityGoodsVO.setFirstStatus(1);
        activityGoodsVO.setIsApp(true);
        return Result.success(activityGoodsCoreService.queryActivityGoodsResult(activityGoodsVO));
    }


    @PostMapping("/activityGoodsListForShop")
    @ApiOperation(value = "商户端查询活动商品列表")
    public Result<PageView<ActivityGoodsResult>> queryActivityGoodsPageForShop(@RequestBody ActivityGoodsVO activityGoodsVO){
//        activityGoodsVO.setIsApp(true);
        activityGoodsVO.setShop(true);
        return Result.success(activityGoodsCoreService.queryActivityGoodsPageForShop(activityGoodsVO));
    }

    @PostMapping("/history/activityGoodsListForShop")
    @ApiOperation(value = "商户端查询归档报名记录")
    public Result<PageView<ActivityGoodsResult>> pageHistoryActivityGoodsRecords(@RequestBody ActivityGoodsVO activityGoodsVO) {
        activityGoodsVO.setShop(true);
        return Result.success(activityGoodsCoreService.pageHistoryActivityGoodsRecords(activityGoodsVO));
    }

    @PostMapping("/records/activityGoodsListForShop")
    @ApiOperation(value = "商户端查询活动报名记录")
    public Result<PageView<ActivityGoodsResult>> pageActivityGoodsRecords(@RequestBody ActivityGoodsVO activityGoodsVO) {
        activityGoodsVO.setShop(true);
        return Result.success(activityGoodsCoreService.pageActivityGoodsRecords(activityGoodsVO));
    }


    @PostMapping("goods/update")
    @ApiOperation(value = "更新活动商品排序")
    public Result<Boolean> updateActivityGoods(@RequestBody ActivityGoodsVO vo) {
        Boolean success = activityGoodsCoreService.updateActivityGoods(vo);
        return Result.success(success);
    }



    @PostMapping("/insert")
    @ApiOperation(value = "新增活动")
    public Result<Boolean> insertActivity(@RequestBody ActivityVO activityVO) {
        return Result.success(activityCoreService.insertActivity(activityVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "编辑活动")
    public Result<Boolean> updateActivity(@RequestBody ActivityVO activityVO) {
        return Result.success(activityCoreService.updateActivity(activityVO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除活动")
    public Result<Boolean> deleteActivity(@RequestBody ActivityVO activityVO) {
        return Result.success(activityCoreService.deleteActivity(activityVO));
    }

    @PostMapping("/label/config")
    @ApiOperation(value = "标签图标签配置")
    public Result<Boolean> bindLabelTagId(@RequestBody LabelTagBindVO vo) {
        return Result.success(activityCoreService.bindLabelTagId(vo));
    }

//    @PostMapping("config/add")
//    @ApiOperation(value = "新增活动配置")
//    public Result<Boolean> addActivityConfig(@RequestBody ActivityConfigVo vo) {
//        Boolean success = activityConfigCoreService.insertActivityConfig(vo);
//        return Result.success(success);
//    }

//    @PostMapping("config/update")
//    @ApiOperation(value = "更新活动配置")
//    public Result<Boolean> updateActivityConfig(@RequestBody ActivityConfigVo vo) {
//        Boolean success = activityConfigCoreService.updateActivityConfig(vo);
//        return Result.success(success);
//    }

    @PostMapping("goods/signUpByOperator")
    @ApiOperation(value = "运营新增活动商品")
    public Result<ActivityGoodsSignUpResultVO> signUpByOperator(@RequestBody ActivityGoodsSignUpVo vo) {
        ActivityGoodsSignUpResultVO resultVO = activityGoodsCoreService.signUpByOperator(vo, false);
        return Result.success(resultVO);
    }

    @RequestMapping("goods/signUpByShopKeeper")
    @ApiOperation(value = "商家报名活动商品")
    public Result<Boolean> signUpByShopKeeper(@RequestBody ActivityGoodsSignUpVo vo) {
        Boolean success = activityGoodsCoreService.signUpByShopKeeper(vo);
        return Result.success(success);
    }




    @PostMapping("goods/signUpByImport")
    @ApiOperation(value = "商家报名商品(导入)")
    public Result<Boolean> signUp(@RequestParam("file") MultipartFile file, @RequestParam("activityId") Long activityId) throws IOException {
        EasyExcel.read(file.getInputStream(), ActivityGoodsSignUpByImportVo.class, new ActivitySignUpImportListener(activityId, activityGoodsCoreService))
                .sheet()
                .doRead();
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("goods/downLoadSignUpTemplate")
    @ApiOperation(value = "下载活动商品报名模板 ")
    public Result<Boolean> downLoadActivityGoodsSignUpTemplate() throws IOException {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=activityGoodsSignUpTemplate.xlsx");
        EasyExcel.write(response.getOutputStream(), ActivityGoodsSignUpByImportVo.class)
                .sheet()
                .doWrite(new ArrayList<ActivityGoodsSignUpByImportVo>());
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("goods/downLoadSortTemplate")
    @ApiOperation(value = "下载活动商品排序模板 ")
    public Result<Boolean> downLoadActivityGoodsSortTemplate() throws IOException {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=activityGoodsSortTemplate.xlsx");
        EasyExcel.write(response.getOutputStream(), ActivityGoodsSortVo.class)
                .sheet()
                .doWrite(new ArrayList<ActivityGoodsSortVo>());
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("exportActivityGoods")
    @ApiOperation(value = "活动管理-商品导出 ")
    public Result<Void> exportActivityGoods(@RequestBody ActivityGoodsVO activityGoodsVO) throws IOException {
        if(Objects.isNull(activityGoodsVO)){
            activityGoodsVO = new ActivityGoodsVO();
        }
        activityGoodsCoreService.asyncExportActivityGoods(activityGoodsVO);
        return Result.success(null);
    }

    @PostMapping("exportActivityGoodsForShop")
    @ApiOperation(value = "商家导出选定活动商品 ")
    public Result<Boolean> ExportActivityGoodsForShop(@RequestBody ActivityGoodsVO activityGoodsVO) throws IOException {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=activityGoodsSortTemplate.xlsx");
        EasyExcel.write(response.getOutputStream(), ActivityGoodsShopExport.class)
                .sheet()
                .doWrite(activityGoodsCoreService.exportActivityGoodsForShop(activityGoodsVO));
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("goods/importSort")
    @ApiOperation(value = "导入活动商品排序")
    public Result<Boolean> importActivityGoodsSort(@RequestParam("file") MultipartFile file, @RequestParam("activityId") Long activityId) throws IOException {
        EasyExcel.read(file.getInputStream(), ActivityGoodsSortVo.class, new ActivityGoodsSortImportListener(activityId, activityGoodsCoreService))
                .sheet()
                .doRead();
        return Result.success(Boolean.TRUE);
    }


    @PostMapping("goods/selectOrLost")
    @ApiOperation(value = "入选/落选活动商品")
    public Result<Boolean> selectOrLostActivityGoods(@RequestBody ActivityGoodsSelectVo vo) {
        Boolean success = activityGoodsCoreService.selectOrLostActivityGoods(vo);
        return Result.success(success);
    }

    @PostMapping("/goods/firstSelectOrLost")
    @ApiOperation(value = "初始 入选/落选活动商品")
    public Result<Boolean> firstSelectOrLostActivityGoods(@RequestBody ActivityGoodsSelectVo vo) {
        return Result.success(activityGoodsCoreService.firstSelectOrLostActivityGoods(vo));
    }


    @PostMapping("/first/exportActivityGoods")
    @ApiOperation(value = "导出选定活动商品 ")
    public Result<Boolean> firstExportActivityGoods(@RequestBody ActivityGoodsVO activityGoodsVO) throws IOException {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=activityGoodsSortTemplate.xlsx");
        EasyExcel.write(response.getOutputStream(), ActivityGoodsExport.class)
                .sheet()
                .doWrite(activityGoodsCoreService.exportActivityGoods(activityGoodsVO));
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("/bind/virGoodsIds")
    @ApiOperation(value = "绑定虚拟商品列表id")
    public Result<Boolean> bindVirGoodsItemsId(@RequestBody BindVirGoodsIdVO bindVirGoodsIdVO) {
        return Result.success(activityGoodsCoreService.bindVirGoodsItemsId(bindVirGoodsIdVO));
    }

    @PostMapping("/sync/virGoodsItems")
    @ApiOperation(value = "同步虚拟商品列表")
    public Result<Boolean> syncVirGoodsItems(@RequestBody SyncVirGoodsIdVO syncVirGoodsIdVO){
        syncVirGoodsIdVO.setBackend(true);
        return Result.success(activityGoodsCoreService.syncVirGoodsItems(syncVirGoodsIdVO));
    }
    @PostMapping("/remove/virGoodsItems")
    @ApiOperation(value = "移除虚拟商品列表")
    public Result<Boolean> removeVirGoodsItems(@RequestBody SyncVirGoodsIdVO syncVirGoodsIdVO){
        return Result.success(activityGoodsCoreService.removeVirGoodsItems(syncVirGoodsIdVO));
    }

    @PostMapping("/openNewActivity")
    @ApiOperation(value = "重新打开活动")
    public Result<Void> openNewActivity(@RequestParam("activityId") Long activityId){
        activityCoreService.openNewActivity(activityId);
        return Result.success(null);
    }
    @PostMapping("/closeNewActivity")
    @ApiOperation(value = "关闭活动")
    public Result<Void> closeNewActivity(@RequestParam("activityId") Long activityId){
        activityCoreService.closeNewActivity(activityId);
        return Result.success(null);
    }

    @PostMapping("/pageShopSignUpGoods")
    @ApiOperation(value = "查询店铺可报名商品列表")
    public Result<PageView<GoodsDTO>> pageShopSignUpGoods(@RequestBody ActivityShopLimitVO vo){
        return Result.success(activityGoodsCoreService.pageSignUpGoods(vo));
    }
    @PostMapping("/asyncExport/{id}")
    @ApiOperation(value = "导出终审通过商品列表")
    public Result<Void> asyncExport(@PathVariable Long id){
        activityGoodsCoreService.asyncExport(id);
        return Result.success(null);
    }

    @PostMapping("/removeEffectActivityGoods")
    @ApiOperation(value = "活动进行中商品移除")
    @NoLogin
    public Result<Void> removeEffectActivityGoods(@RequestBody List<Long> idList){
        activityGoodsCoreService.removeEffectActivityGoods(idList);
        return Result.success(null);
    }
    @PostMapping("/listFailReason")
    @ApiOperation(value = "获取失败原因")
    public Result<List<String>> listFailReason(@RequestBody FailReason failReason){
        return Result.success(activityGoodsCoreService.listFailReason(failReason.getFailReason()));
    }

    @PostMapping("/signByFile")
    @ApiOperation(value = "文件报名")
    public Result<Void> signByFile(@RequestParam("file") MultipartFile file){
        activityGoodsCoreService.signByFile(file);
        return Result.success(null);
    }

    @PostMapping("/downloadModel")
    @ApiOperation(value = "下载模版")
    public Result<Void> downloadModel() throws IOException {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=activityGoodsSortTemplate.xlsx");
        EasyExcel.write(response.getOutputStream(), ActivityGoodsSignUpVO.class)
                .sheet()
                .doWrite(new ArrayList<ActivityGoodsSignUpVO>());
        return Result.success(null);
    }

    @PostMapping("/pageGoodsSignUpVO")
    @ApiOperation(value = "查询报名商品")
    public Result<PageView<GoodsSignUpVO>> pageGoodsSignUpVO(@RequestBody GoodsSignUpQueryVO queryVO){
        return Result.success(activityGoodsCoreService.pageGoodsSignUpVO(queryVO));
    }

    @PostMapping("/likeQuery/{name}")
    @ApiOperation(value = "活动名查询")
    Result<List<ActivityIdVO>> likeQuery(@PathVariable(required = false, value = "name") String name){
        return Result.success(activityCoreService.likeQuery(name));
    }

    @GetMapping("/handSelect")
    @ApiOperation(value = "手动跑flashDeal活动")
    @NoLogin
    public Result<Boolean> handSelect(@RequestParam("activityId") Long activityId) {
        activityGoodsCoreService.handSelect(activityId);
        return Result.success(true);
    }

    @GetMapping("/goods/init")
    @NoLogin
    public Result<Boolean> initActivityGoodsInfo() {
        activityGoodsCoreService.initActivityGoodsInfo();
        return Result.success(true);
    }

    @GetMapping("/handleRepeatSignUpGoods")
    @ApiOperation(value = "处理重复报名的活动上篇")
    @NoLogin
    public Result<Void> handleRepeatSignUpGoods(@RequestParam("activityId") Long activityId) {
        activityGoodsCoreService.handleRepeatSignUpGoods(activityId);
        return Result.success(null);
    }

}
