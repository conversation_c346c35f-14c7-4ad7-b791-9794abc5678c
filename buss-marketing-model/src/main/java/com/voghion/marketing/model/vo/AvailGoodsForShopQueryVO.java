package com.voghion.marketing.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 商家可报名商品列表查询参数VO
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-17
 */
@Data
public class AvailGoodsForShopQueryVO extends PageParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动ID", required = true)
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    @ApiModelProperty(value = "商家ID", required = true)
    @NotNull(message = "商家ID不能为空")
    private Long shopId;

    @ApiModelProperty(value = "商品名称（模糊搜索）")
    private String goodsName;

    @ApiModelProperty(value = "商品ID")
    private Long goodsId;

    @ApiModelProperty(value = "类目ID")
    private Long categoryId;

    @ApiModelProperty(value = "品牌ID")
    private Long brandId;

    @ApiModelProperty(value = "商品状态：1-上架，0-下架")
    private Integer goodsStatus;

    @ApiModelProperty(value = "预审状态：1-通过，0-未通过，null-全部")
    private Integer preAuditStatus;

    @ApiModelProperty(value = "最低价格")
    private Double minPrice;

    @ApiModelProperty(value = "最高价格")
    private Double maxPrice;

    @ApiModelProperty(value = "排序字段：createTime-创建时间，sales-销量，price-价格")
    private String sortField;

    @ApiModelProperty(value = "排序方向：asc-升序，desc-降序")
    private String sortDirection;

    @ApiModelProperty(value = "是否强制刷新缓存")
    private Boolean forceRefresh = false;
}
