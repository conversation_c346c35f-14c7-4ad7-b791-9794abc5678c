package com.voghion.marketing.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商家可报名商品列表返回结果VO
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-17
 */
@Data
public class AvailGoodsForShopVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品ID")
    private Long goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品主图")
    private String mainImage;

    @ApiModelProperty(value = "商品编号")
    private String itemNumber;

    @ApiModelProperty(value = "类目ID")
    private Long categoryId;

    @ApiModelProperty(value = "类目路径")
    private String categoryPath;

    @ApiModelProperty(value = "品牌ID")
    private Long brandId;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "商家ID")
    private Long shopId;

    @ApiModelProperty(value = "商家名称")
    private String shopName;

    @ApiModelProperty(value = "商品状态：1-上架，0-下架")
    private Integer goodsStatus;

    @ApiModelProperty(value = "商品状态描述")
    private String goodsStatusDesc;

    @ApiModelProperty(value = "最低价格")
    private BigDecimal minPrice;

    @ApiModelProperty(value = "最高价格")
    private BigDecimal maxPrice;

    @ApiModelProperty(value = "最低市场价")
    private BigDecimal minMarketPrice;

    @ApiModelProperty(value = "最高市场价")
    private BigDecimal maxMarketPrice;

    @ApiModelProperty(value = "最低拼团价")
    private BigDecimal minGrouponPrice;

    @ApiModelProperty(value = "最高拼团价")
    private BigDecimal maxGrouponPrice;

    @ApiModelProperty(value = "销量")
    private Long sales;

    @ApiModelProperty(value = "商品创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "预审状态：1-通过，0-未通过")
    private Integer preAuditStatus;

    @ApiModelProperty(value = "预审状态描述")
    private String preAuditStatusDesc;

    @ApiModelProperty(value = "预审失败原因")
    private String preAuditFailReason;

    @ApiModelProperty(value = "预审失败原因列表")
    private List<String> preAuditFailReasons;

    @ApiModelProperty(value = "预审时间")
    private LocalDateTime preAuditTime;

    @ApiModelProperty(value = "是否已报名当前活动：true-已报名，false-未报名")
    private Boolean isSignedUp;

    @ApiModelProperty(value = "报名状态：1-已报名，0-未报名")
    private Integer signUpStatus;

    @ApiModelProperty(value = "报名时间")
    private LocalDateTime signUpTime;

    @ApiModelProperty(value = "活动审核状态：1-入选，0-落选，-1-待审核")
    private Integer activityAuditStatus;

    @ApiModelProperty(value = "活动审核状态描述")
    private String activityAuditStatusDesc;

    @ApiModelProperty(value = "是否可以报名：true-可以，false-不可以")
    private Boolean canSignUp;

    @ApiModelProperty(value = "不可报名原因")
    private String cannotSignUpReason;

    @ApiModelProperty(value = "商品标签列表")
    private List<String> goodsTags;

    @ApiModelProperty(value = "商品属性信息")
    private String propertyValues;

    @ApiModelProperty(value = "库存数量")
    private Integer stockQuantity;

    @ApiModelProperty(value = "是否有库存：true-有库存，false-无库存")
    private Boolean hasStock;
}
